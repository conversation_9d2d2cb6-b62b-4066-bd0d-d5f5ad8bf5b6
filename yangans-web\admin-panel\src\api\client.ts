import axios, { type AxiosInstance, type AxiosRequestConfig, type AxiosResponse } from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { ApiResponse } from '@/types'
import router from '@/router'

// 创建axios实例
const apiClient: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    // 🔒 正常认证模式：从localStorage获取token
    const token = localStorage.getItem('admin-token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    // 添加时间戳防止缓存
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }

    return config
  },
  (error) => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    const { data } = response
    
    // 如果是文件下载等特殊响应，直接返回
    if (response.config.responseType === 'blob') {
      return response
    }
    
    // 检查业务状态码
    if (data.code === 200) {
      return response
    } else if (data.code === 401) {
      // 未授权，清除本地存储并跳转到登录页
      handleUnauthorized()
      return Promise.reject(new Error(data.message || '未授权访问'))
    } else if (data.code === 403) {
      // 权限不足
      ElMessage.error(data.message || '权限不足')
      return Promise.reject(new Error(data.message || '权限不足'))
    } else {
      // 其他业务错误
      ElMessage.error(data.message || '请求失败')
      return Promise.reject(new Error(data.message || '请求失败'))
    }
  },
  async (error) => {
    console.error('响应拦截器错误:', error)
    
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          // 🔒 正常认证模式：尝试刷新token
          const refreshed = await tryRefreshToken()
          if (refreshed) {
            // 重新发送原请求
            return apiClient.request(error.config)
          } else {
            handleUnauthorized()
          }
          break
        case 403:
          ElMessage.error('权限不足')
          break
        case 404:
          ElMessage.error('请求的资源不存在')
          break
        case 500:
          ElMessage.error('服务器内部错误')
          break
        case 502:
        case 503:
        case 504:
          ElMessage.error('服务器暂时不可用，请稍后重试')
          break
        default:
          ElMessage.error(data?.message || `请求失败 (${status})`)
      }
    } else if (error.code === 'ECONNABORTED') {
      ElMessage.error('请求超时，请检查网络连接')
    } else if (error.message === 'Network Error') {
      ElMessage.error('网络连接失败，请检查网络设置')
    } else {
      ElMessage.error(error.message || '请求失败')
    }
    
    return Promise.reject(error)
  }
)

// 处理未授权访问
const handleUnauthorized = () => {
  // 🔒 正常认证模式：处理未授权访问
  // 清除本地存储
  localStorage.removeItem('admin-token')
  localStorage.removeItem('admin-refresh-token')
  localStorage.removeItem('admin-user')

  // 跳转到登录页
  if (window.location.pathname !== '/login') {
    ElMessageBox.confirm(
      '登录状态已过期，请重新登录',
      '提示',
      {
        confirmButtonText: '重新登录',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      router.push('/login')
    }).catch(() => {
      router.push('/login')
    })
  }
}

// 尝试刷新token
const tryRefreshToken = async (): Promise<boolean> => {
  try {
    const refreshToken = localStorage.getItem('admin-refresh-token')
    if (!refreshToken) {
      return false
    }
    
    const response = await axios.post(
      `${import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080/api'}/admin/auth/refresh`,
      { refreshToken }
    )
    
    if (response.data.code === 200) {
      const { token, refreshToken: newRefreshToken } = response.data.data
      localStorage.setItem('admin-token', token)
      localStorage.setItem('admin-refresh-token', newRefreshToken)
      return true
    }
    
    return false
  } catch (error) {
    console.error('刷新token失败:', error)
    return false
  }
}

// 封装常用的请求方法
export const request = {
  get: <T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> => {
    return apiClient.get(url, config).then(res => res.data)
  },
  
  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> => {
    return apiClient.post(url, data, config).then(res => res.data)
  },
  
  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> => {
    return apiClient.put(url, data, config).then(res => res.data)
  },
  
  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> => {
    return apiClient.patch(url, data, config).then(res => res.data)
  },
  
  delete: <T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> => {
    return apiClient.delete(url, config).then(res => res.data)
  },
  
  upload: <T = any>(url: string, formData: FormData, config?: AxiosRequestConfig): Promise<ApiResponse<T>> => {
    return apiClient.post(url, formData, {
      ...config,
      headers: {
        'Content-Type': 'multipart/form-data',
        ...config?.headers
      }
    }).then(res => res.data)
  },
  
  download: (url: string, config?: AxiosRequestConfig): Promise<AxiosResponse> => {
    return apiClient.get(url, {
      ...config,
      responseType: 'blob'
    })
  }
}

export default apiClient
