// 用户相关类型
export interface User {
  id: number
  username: string
  email: string
  nickname: string
  avatar?: string
  role: string
  status: number
  lastLoginTime?: string
  lastLoginIp?: string
  createdAt: string
  updatedAt: string
}

export interface LoginForm {
  username: string
  password: string
  remember?: boolean
}

export interface LoginResponse {
  accessToken: string
  tokenType: string
  expiresIn: number
  userInfo: User
  loginTime: string
}

// 文章相关类型
export interface Article {
  id: string
  title: string
  content: string
  summary?: string
  cover?: string
  status: 'draft' | 'published' | 'archived'
  categoryId: string
  category?: Category
  tags: Tag[]
  author: User
  viewCount: number
  likeCount: number
  commentCount: number
  isTop: boolean
  publishedAt?: string
  createdAt: string
  updatedAt: string
}

export interface ArticleForm {
  title: string
  content: string
  summary?: string
  cover?: string
  status: 'draft' | 'published'
  categoryId: string
  tagIds: string[]
  isTop: boolean
  publishedAt?: string
}

export interface Category {
  id: string
  name: string
  description?: string
  sort: number
  articleCount: number
  createdAt: string
  updatedAt: string
}

export interface Tag {
  id: string
  name: string
  color?: string
  articleCount: number
  createdAt: string
  updatedAt: string
}

// 留言相关类型
export interface Message {
  id: string
  nickname: string
  email?: string
  content: string
  isAnonymous: boolean
  status: 'pending' | 'approved' | 'rejected'
  ip?: string
  userAgent?: string
  reply?: string
  repliedAt?: string
  createdAt: string
  updatedAt: string
}

export interface MessageForm {
  nickname: string
  email?: string
  content: string
  isAnonymous: boolean
}

// 生活记录相关类型
export interface LifeRecord {
  id: string
  content: string
  images?: string[]
  location?: string
  weather?: string
  mood: 'happy' | 'sad' | 'excited' | 'calm' | 'tired'
  tags?: string[]
  isPublic: boolean
  createdAt: string
  updatedAt: string
}

export interface LifeRecordForm {
  content: string
  images?: string[]
  location?: string
  weather?: string
  mood: 'happy' | 'sad' | 'excited' | 'calm' | 'tired'
  tags?: string[]
  isPublic: boolean
}

// 番剧相关类型
export interface Anime {
  id: string
  title: string
  description?: string
  cover?: string
  year: number
  episodes: number
  genre: string
  status: 'want_to_watch' | 'watching' | 'completed' | 'dropped'
  rating?: number
  progress: number
  startDate?: string
  endDate?: string
  createdAt: string
  updatedAt: string
}

export interface AnimeForm {
  title: string
  description?: string
  cover?: string
  year: number
  episodes: number
  genre: string
  status: 'want_to_watch' | 'watching' | 'completed' | 'dropped'
  rating?: number
  progress: number
  startDate?: string
  endDate?: string
}

// 统计相关类型
export interface DashboardStats {
  articleCount: number
  messageCount: number
  lifeRecordCount: number
  animeCount: number
  totalViews: number
  todayViews: number
  recentArticles: Article[]
  recentMessages: Message[]
}

// API响应类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

export interface PaginationParams {
  page: number
  pageSize: number
  keyword?: string
  status?: string
  categoryId?: string
  tagId?: string
  startDate?: string
  endDate?: string
}

export interface PaginationResponse<T> {
  list: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 文件上传相关类型
export interface UploadFile {
  id: string
  name: string
  url: string
  size: number
  type: string
  createdAt: string
}

export interface UploadResponse {
  url: string
  filename: string
  size: number
}

// 系统设置相关类型
export interface SystemSettings {
  siteName: string
  siteDescription: string
  siteKeywords: string
  siteUrl: string
  adminEmail: string
  enableComment: boolean
  enableMessage: boolean
  enableRegistration: boolean
  uploadMaxSize: number
  allowedFileTypes: string[]
}

// 主题相关类型
export type Theme = 'light' | 'dark' | 'auto'

// 菜单相关类型
export interface MenuItem {
  id: string
  title: string
  icon?: string
  path?: string
  children?: MenuItem[]
  meta?: {
    requiresAuth?: boolean
    roles?: string[]
    hidden?: boolean
  }
}
