package com.yangans.admin.component.web;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.beans.factory.annotation.Value;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * CORS跨域配置
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Configuration
public class CorsConfig {

    @Value("${yangans.cors.allowed-origins:http://localhost:3000,http://localhost:3001,http://localhost:3002,http://localhost:3003}")
    private List<String> allowedOrigins;

    @Value("${yangans.cors.allowed-methods:GET,POST,PUT,DELETE,OPTIONS}")
    private String allowedMethods;

    @Value("${yangans.cors.allowed-headers:*}")
    private String allowedHeaders;

    @Value("${yangans.cors.allow-credentials:true}")
    private Boolean allowCredentials;

    @Value("${yangans.cors.max-age:3600}")
    private Long maxAge;

    // Getters and Setters
    public List<String> getAllowedOrigins() {
        return allowedOrigins;
    }

    public void setAllowedOrigins(List<String> allowedOrigins) {
        this.allowedOrigins = allowedOrigins;
    }

    public String getAllowedMethods() {
        return allowedMethods;
    }

    public void setAllowedMethods(String allowedMethods) {
        this.allowedMethods = allowedMethods;
    }

    public String getAllowedHeaders() {
        return allowedHeaders;
    }

    public void setAllowedHeaders(String allowedHeaders) {
        this.allowedHeaders = allowedHeaders;
    }

    public Boolean getAllowCredentials() {
        return allowCredentials;
    }

    public void setAllowCredentials(Boolean allowCredentials) {
        this.allowCredentials = allowCredentials;
    }

    public Long getMaxAge() {
        return maxAge;
    }

    public void setMaxAge(Long maxAge) {
        this.maxAge = maxAge;
    }

    /**
     * CORS配置源 - 简化版本，确保预检请求正常工作
     */
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        System.out.println("🔧 初始化CORS配置...");
        System.out.println("允许的源: " + allowedOrigins);
        System.out.println("允许的方法: " + allowedMethods);
        System.out.println("允许的头部: " + allowedHeaders);

        CorsConfiguration configuration = new CorsConfiguration();

        // 🚧 开发模式：使用更宽松的CORS配置
        configuration.setAllowedOriginPatterns(Arrays.asList("*")); // 允许所有源
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH"));
        configuration.setAllowedHeaders(Arrays.asList("*")); // 允许所有头部
        configuration.setAllowCredentials(true); // 允许凭证
        configuration.setMaxAge(3600L); // 预检请求缓存1小时

        // 暴露的头部
        configuration.setExposedHeaders(Arrays.asList(
                "Authorization",
                "Content-Type",
                "X-Requested-With",
                "Accept",
                "Origin",
                "Access-Control-Request-Method",
                "Access-Control-Request-Headers",
                "Access-Control-Allow-Origin",
                "Access-Control-Allow-Credentials"
        ));

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();

        // 为所有路径应用相同的CORS配置
        source.registerCorsConfiguration("/**", configuration);

        System.out.println("✅ CORS配置初始化完成");
        return source;
    }
}
