package com.yangans.admin.controller.admin;

import com.yangans.admin.common.Result;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 测试控制器 - 用于验证CORS配置
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@RestController
@RequestMapping("/api/admin/test")
public class TestController {

    /**
     * CORS测试接口
     */
    @GetMapping("/cors")
    public Result<Map<String, Object>> testCors() {
        Map<String, Object> data = new HashMap<>();
        data.put("message", "CORS测试成功");
        data.put("timestamp", LocalDateTime.now());
        data.put("server", "YangAns Admin Backend");
        
        System.out.println("✅ CORS测试接口被调用");
        return Result.success(data);
    }

    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public Result<Map<String, Object>> health() {
        Map<String, Object> data = new HashMap<>();
        data.put("status", "UP");
        data.put("timestamp", LocalDateTime.now());
        
        return Result.success(data);
    }

    /**
     * POST请求测试
     */
    @PostMapping("/echo")
    public Result<Map<String, Object>> echo(@RequestBody Map<String, Object> request) {
        Map<String, Object> data = new HashMap<>();
        data.put("received", request);
        data.put("timestamp", LocalDateTime.now());
        
        System.out.println("✅ POST测试接口被调用，接收到数据: " + request);
        return Result.success(data);
    }
}
