import { request } from './client'
import type { LoginForm, LoginResponse, User, ApiResponse } from '@/types'

export const authApi = {
  // 登录
  login: (data: LoginForm): Promise<ApiResponse<LoginResponse>> => {
    return request.post('/admin/auth/login', data)
  },

  // 登出
  logout: (): Promise<ApiResponse> => {
    return request.post('/admin/auth/logout')
  },

  // 刷新token
  refreshToken: (refreshToken: string): Promise<ApiResponse<{ token: string; refreshToken: string }>> => {
    return request.post('/admin/auth/refresh', { refreshToken })
  },

  // 获取用户信息
  getUserInfo: (): Promise<ApiResponse<User>> => {
    return request.get('/admin/auth/info')
  },
  
  // 更新用户信息
  updateUserInfo: (data: Partial<User>): Promise<ApiResponse<User>> => {
    return request.put('/admin/auth/user', data)
  },

  // 修改密码
  changePassword: (data: { oldPassword: string; newPassword: string }): Promise<ApiResponse> => {
    return request.post('/admin/auth/change-password', data)
  }
}
