package com.yangans.admin.controller.public_;

import com.yangans.admin.common.PageResult;
import com.yangans.admin.common.Result;
import com.yangans.admin.domain.dto.ArticleQueryDTO;
import com.yangans.admin.domain.vo.ArticleVO;
import com.yangans.admin.service.public_.PublicArticleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 主站文章控制器
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@RestController
@RequestMapping("/public/articles")
@RequiredArgsConstructor
@Validated
@Api(tags = "主站-文章展示")
public class PublicArticleController {

    private final PublicArticleService publicArticleService;

    /**
     * 分页查询已发布文章列表
     *
     * @param query 查询条件
     * @return 文章分页列表
     */
    @GetMapping
    @ApiOperation("分页查询已发布文章列表")
    public Result<PageResult<ArticleVO>> getPublishedArticles(@Valid ArticleQueryDTO query) {
        log.debug("主站查询文章列表：{}", query);
        
        PageResult<ArticleVO> pageResult = publicArticleService.getPublishedArticles(query);
        return Result.success(pageResult);
    }

    /**
     * 根据ID查询文章详情
     *
     * @param id 文章ID
     * @return 文章详情
     */
    @GetMapping("/{id}")
    @ApiOperation("根据ID查询文章详情")
    public Result<ArticleVO> getArticleById(@ApiParam("文章ID") @PathVariable Long id) {
        log.debug("主站查询文章详情：{}", id);
        
        ArticleVO articleVO = publicArticleService.getArticleById(id);
        return Result.success(articleVO);
    }

    /**
     * 获取热门文章
     *
     * @param limit 限制数量
     * @return 热门文章列表
     */
    @GetMapping("/hot")
    @ApiOperation("获取热门文章")
    public Result<List<ArticleVO>> getHotArticles(@RequestParam(defaultValue = "10") Integer limit) {
        log.debug("主站获取热门文章：limit={}", limit);
        
        List<ArticleVO> hotArticles = publicArticleService.getHotArticles(limit);
        return Result.success(hotArticles);
    }

    /**
     * 获取最新文章
     *
     * @param limit 限制数量
     * @return 最新文章列表
     */
    @GetMapping("/latest")
    @ApiOperation("获取最新文章")
    public Result<List<ArticleVO>> getLatestArticles(@RequestParam(defaultValue = "10") Integer limit) {
        log.debug("主站获取最新文章：limit={}", limit);
        
        List<ArticleVO> latestArticles = publicArticleService.getLatestArticles(limit);
        return Result.success(latestArticles);
    }

    /**
     * 获取推荐文章（置顶文章）
     *
     * @param limit 限制数量
     * @return 推荐文章列表
     */
    @GetMapping("/recommended")
    @ApiOperation("获取推荐文章")
    public Result<List<ArticleVO>> getRecommendedArticles(@RequestParam(defaultValue = "5") Integer limit) {
        log.debug("主站获取推荐文章：limit={}", limit);
        
        List<ArticleVO> recommendedArticles = publicArticleService.getRecommendedArticles(limit);
        return Result.success(recommendedArticles);
    }

    /**
     * 根据分类ID查询文章列表
     *
     * @param categoryId 分类ID
     * @param current    当前页
     * @param size       每页大小
     * @return 文章分页列表
     */
    @GetMapping("/category/{categoryId}")
    @ApiOperation("根据分类查询文章列表")
    public Result<PageResult<ArticleVO>> getArticlesByCategory(@ApiParam("分类ID") @PathVariable Long categoryId,
                                                               @RequestParam(defaultValue = "1") Long current,
                                                               @RequestParam(defaultValue = "10") Long size) {
        log.debug("主站根据分类查询文章：categoryId={}, current={}, size={}", categoryId, current, size);
        
        PageResult<ArticleVO> pageResult = publicArticleService.getArticlesByCategory(categoryId, current, size);
        return Result.success(pageResult);
    }

    /**
     * 根据标签ID查询文章列表
     *
     * @param tagId   标签ID
     * @param current 当前页
     * @param size    每页大小
     * @return 文章分页列表
     */
    @GetMapping("/tag/{tagId}")
    @ApiOperation("根据标签查询文章列表")
    public Result<PageResult<ArticleVO>> getArticlesByTag(@ApiParam("标签ID") @PathVariable Long tagId,
                                                          @RequestParam(defaultValue = "1") Long current,
                                                          @RequestParam(defaultValue = "10") Long size) {
        log.debug("主站根据标签查询文章：tagId={}, current={}, size={}", tagId, current, size);
        
        PageResult<ArticleVO> pageResult = publicArticleService.getArticlesByTag(tagId, current, size);
        return Result.success(pageResult);
    }

    /**
     * 搜索文章
     *
     * @param keyword 搜索关键词
     * @param current 当前页
     * @param size    每页大小
     * @return 文章分页列表
     */
    @GetMapping("/search")
    @ApiOperation("搜索文章")
    public Result<PageResult<ArticleVO>> searchArticles(@RequestParam String keyword,
                                                        @RequestParam(defaultValue = "1") Long current,
                                                        @RequestParam(defaultValue = "10") Long size) {
        log.debug("主站搜索文章：keyword={}, current={}, size={}", keyword, current, size);
        
        PageResult<ArticleVO> pageResult = publicArticleService.searchArticles(keyword, current, size);
        return Result.success(pageResult);
    }

    /**
     * 增加文章浏览量
     *
     * @param id 文章ID
     * @return 操作结果
     */
    @PostMapping("/{id}/view")
    @ApiOperation("增加文章浏览量")
    public Result<Void> incrementViewCount(@ApiParam("文章ID") @PathVariable Long id) {
        log.debug("主站增加文章浏览量：{}", id);
        
        publicArticleService.incrementViewCount(id);
        return Result.success();
    }

    /**
     * 获取文章归档信息
     *
     * @return 归档信息
     */
    @GetMapping("/archives")
    @ApiOperation("获取文章归档信息")
    public Result<List<Object>> getArticleArchives() {
        log.debug("主站获取文章归档信息");
        
        List<Object> archives = publicArticleService.getArticleArchives();
        return Result.success(archives);
    }

    /**
     * 获取相关文章
     *
     * @param id    文章ID
     * @param limit 限制数量
     * @return 相关文章列表
     */
    @GetMapping("/{id}/related")
    @ApiOperation("获取相关文章")
    public Result<List<ArticleVO>> getRelatedArticles(@ApiParam("文章ID") @PathVariable Long id,
                                                      @RequestParam(defaultValue = "5") Integer limit) {
        log.debug("主站获取相关文章：id={}, limit={}", id, limit);
        
        List<ArticleVO> relatedArticles = publicArticleService.getRelatedArticles(id, limit);
        return Result.success(relatedArticles);
    }
}
