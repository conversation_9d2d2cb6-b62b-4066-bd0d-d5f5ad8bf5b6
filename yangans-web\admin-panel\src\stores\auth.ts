import { defineStore } from 'pinia'
import type { User, LoginForm, LoginResponse } from '@/types'
import { authApi } from '@/api/auth'
import { ElMessage } from 'element-plus'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string | null>(localStorage.getItem('admin-token'))
  const refreshToken = ref<string | null>(localStorage.getItem('admin-refresh-token'))
  const user = ref<User | null>(null)
  const isLoading = ref(false)
  
  // 计算属性
  const isLoggedIn = computed(() => !!token.value && !!user.value)
  const isAdmin = computed(() => user.value?.role === 'admin')
  
  // 动作
  const login = async (loginForm: LoginForm): Promise<boolean> => {
    try {
      isLoading.value = true

      // 🔒 正常认证模式：真实的登录API调用
      const response = await authApi.login(loginForm)

      if (response.code === 200) {
        const { accessToken, userInfo } = response.data

        // 保存认证信息
        token.value = accessToken
        user.value = userInfo

        // 保存到本地存储
        localStorage.setItem('admin-token', accessToken)
        localStorage.setItem('admin-user', JSON.stringify(userInfo))

        ElMessage.success('登录成功')
        return true
      } else {
        ElMessage.error(response.message || '登录失败')
        return false
      }
    } catch (error: any) {
      console.error('登录错误:', error)
      ElMessage.error(error.message || '登录失败，请检查网络连接')
      return false
    } finally {
      isLoading.value = false
    }
  }
  
  const logout = async () => {
    try {
      // 调用登出API
      if (token.value) {
        await authApi.logout()
      }
    } catch (error) {
      console.error('登出API调用失败:', error)
    } finally {
      // 清除本地状态
      clearAuthData()
      ElMessage.success('已退出登录')
    }
  }
  
  const refreshAccessToken = async (): Promise<boolean> => {
    try {
      if (!refreshToken.value) {
        throw new Error('没有刷新令牌')
      }
      
      const response = await authApi.refreshToken(refreshToken.value)
      
      if (response.code === 200) {
        const { token: newToken, refreshToken: newRefreshToken } = response.data
        
        token.value = newToken
        refreshToken.value = newRefreshToken
        
        localStorage.setItem('admin-token', newToken)
        localStorage.setItem('admin-refresh-token', newRefreshToken)
        
        return true
      } else {
        throw new Error(response.message || '刷新令牌失败')
      }
    } catch (error) {
      console.error('刷新令牌失败:', error)
      clearAuthData()
      return false
    }
  }
  
  const getUserInfo = async (): Promise<boolean> => {
    try {
      if (!token.value) {
        return false
      }
      
      const response = await authApi.getUserInfo()
      
      if (response.code === 200) {
        user.value = response.data
        localStorage.setItem('admin-user', JSON.stringify(response.data))
        return true
      } else {
        throw new Error(response.message || '获取用户信息失败')
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      clearAuthData()
      return false
    }
  }
  
  const initAuth = async () => {
    // 🔒 正常认证模式：从本地存储恢复用户信息
    const savedUser = localStorage.getItem('admin-user')
    if (savedUser) {
      try {
        user.value = JSON.parse(savedUser)
      } catch (error) {
        console.error('解析用户信息失败:', error)
        localStorage.removeItem('admin-user')
      }
    }

    // 如果有token但没有用户信息，尝试获取用户信息
    if (token.value && !user.value) {
      const success = await getUserInfo()
      if (!success) {
        clearAuthData()
      }
    }
  }
  
  const clearAuthData = () => {
    token.value = null
    refreshToken.value = null
    user.value = null
    
    localStorage.removeItem('admin-token')
    localStorage.removeItem('admin-refresh-token')
    localStorage.removeItem('admin-user')
  }
  
  const updateUserInfo = (userData: Partial<User>) => {
    if (user.value) {
      user.value = { ...user.value, ...userData }
      localStorage.setItem('admin-user', JSON.stringify(user.value))
    }
  }
  
  // 检查权限
  const hasPermission = (permission: string): boolean => {
    if (!user.value) return false
    if (user.value.role === 'admin') return true
    
    // 这里可以根据实际需求扩展权限检查逻辑
    return false
  }
  
  const hasRole = (role: string): boolean => {
    return user.value?.role === role
  }
  
  return {
    // 状态
    token,
    refreshToken,
    user,
    isLoading,
    
    // 计算属性
    isLoggedIn,
    isAdmin,
    
    // 动作
    login,
    logout,
    refreshAccessToken,
    getUserInfo,
    initAuth,
    clearAuthData,
    updateUserInfo,
    hasPermission,
    hasRole
  }
})
