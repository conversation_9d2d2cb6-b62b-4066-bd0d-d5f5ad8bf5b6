import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

public class TestPassword {
    public static void main(String[] args) {
        BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
        
        String password = "admin123";
        String encoded = encoder.encode(password);
        
        System.out.println("原密码: " + password);
        System.out.println("加密后: " + encoded);
        
        // 测试数据库中的密码
        String dbPassword = "$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8ioctqp33Lsqp7.Hk8ZfJON.LjCaG";
        boolean matches = encoder.matches(password, dbPassword);
        System.out.println("数据库密码验证: " + matches);
        
        // 测试新生成的密码
        boolean newMatches = encoder.matches(password, encoded);
        System.out.println("新密码验证: " + newMatches);
    }
}
