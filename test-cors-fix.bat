@echo off
echo ========================================
echo CORS修复验证测试脚本
echo ========================================
echo.

echo [1/5] 检查后端服务状态...
curl -s -o nul -w "HTTP状态码: %%{http_code}" http://localhost:8080/api/admin/test/health
if %errorlevel% neq 0 (
    echo [错误] 后端服务未启动
    echo 请先启动后端服务: cd yangans-admin && mvn spring-boot:run
    pause
    exit /b 1
) else (
    echo [成功] 后端服务正常运行
)
echo.

echo [2/5] 测试简单的GET请求...
curl -s -H "Origin: http://localhost:3002" http://localhost:8080/api/admin/test/cors
if %errorlevel% neq 0 (
    echo [错误] GET请求失败
) else (
    echo [成功] GET请求正常
)
echo.

echo [3/5] 测试CORS预检请求 (OPTIONS)...
curl -s -H "Origin: http://localhost:3002" ^
     -H "Access-Control-Request-Method: GET" ^
     -H "Access-Control-Request-Headers: Authorization" ^
     -X OPTIONS ^
     -v http://localhost:8080/api/admin/test/cors 2>&1 | findstr "Access-Control"
if %errorlevel% neq 0 (
    echo [警告] 未检测到CORS头部
) else (
    echo [成功] CORS头部正常返回
)
echo.

echo [4/5] 测试分类API的CORS...
curl -s -H "Origin: http://localhost:3002" ^
     -H "Access-Control-Request-Method: GET" ^
     -H "Access-Control-Request-Headers: Authorization" ^
     -X OPTIONS ^
     -v http://localhost:8080/api/admin/categories 2>&1 | findstr "Access-Control"
if %errorlevel% neq 0 (
    echo [警告] 分类API CORS头部未检测到
) else (
    echo [成功] 分类API CORS头部正常
)
echo.

echo [5/5] 测试文章API的CORS...
curl -s -H "Origin: http://localhost:3002" ^
     -H "Access-Control-Request-Method: GET" ^
     -H "Access-Control-Request-Headers: Authorization" ^
     -X OPTIONS ^
     -v http://localhost:8080/api/admin/articles 2>&1 | findstr "Access-Control"
if %errorlevel% neq 0 (
    echo [警告] 文章API CORS头部未检测到
) else (
    echo [成功] 文章API CORS头部正常
)
echo.

echo ========================================
echo 测试完成！
echo ========================================
echo.
echo 如果所有测试都显示[成功]，说明CORS配置已修复。
echo 现在可以启动前端服务测试：
echo   cd yangans-web/admin-panel
echo   npm run dev
echo.
echo 然后访问 http://localhost:3002 检查是否还有CORS错误。
echo.
pause
