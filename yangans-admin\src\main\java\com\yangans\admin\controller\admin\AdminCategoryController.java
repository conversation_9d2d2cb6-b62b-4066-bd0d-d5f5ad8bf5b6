package com.yangans.admin.controller.admin;

import com.yangans.admin.common.Result;
import com.yangans.admin.domain.dto.CategoryDTO;
import com.yangans.admin.domain.vo.CategoryVO;
import com.yangans.admin.service.admin.AdminCategoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 管理后台分类控制器
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@RestController
@RequestMapping("/admin/categories")
@RequiredArgsConstructor
@Validated
@Api(tags = "管理后台-分类管理")
public class AdminCategoryController {

    private final AdminCategoryService adminCategoryService;

    /**
     * 获取所有分类列表
     *
     * @return 分类列表
     */
    @GetMapping
    @ApiOperation("获取所有分类列表")
    public Result<List<CategoryVO>> getAllCategories() {
        log.debug("管理后台获取所有分类列表");
        
        List<CategoryVO> categories = adminCategoryService.getAllCategories();
        return Result.success(categories);
    }

    /**
     * 根据ID查询分类详情
     *
     * @param id 分类ID
     * @return 分类详情
     */
    @GetMapping("/{id}")
    @ApiOperation("根据ID查询分类详情")
    public Result<CategoryVO> getCategoryById(@ApiParam("分类ID") @PathVariable Long id) {
        log.debug("管理后台查询分类详情：{}", id);
        
        CategoryVO categoryVO = adminCategoryService.getCategoryById(id);
        return Result.success(categoryVO);
    }

    /**
     * 创建分类
     *
     * @param categoryDTO 分类信息
     * @return 创建结果
     */
    @PostMapping
    @ApiOperation("创建分类")
    public Result<CategoryVO> createCategory(@Valid @RequestBody CategoryDTO categoryDTO) {
        log.info("管理后台创建分类：{}", categoryDTO.getName());
        
        CategoryVO categoryVO = adminCategoryService.createCategory(categoryDTO);
        return Result.success("分类创建成功", categoryVO);
    }

    /**
     * 更新分类
     *
     * @param id          分类ID
     * @param categoryDTO 分类信息
     * @return 更新结果
     */
    @PutMapping("/{id}")
    @ApiOperation("更新分类")
    public Result<CategoryVO> updateCategory(@ApiParam("分类ID") @PathVariable Long id,
                                             @Valid @RequestBody CategoryDTO categoryDTO) {
        log.info("管理后台更新分类：{}", id);
        
        categoryDTO.setId(id);
        CategoryVO categoryVO = adminCategoryService.updateCategory(categoryDTO);
        return Result.success("分类更新成功", categoryVO);
    }

    /**
     * 删除分类
     *
     * @param id 分类ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    @ApiOperation("删除分类")
    public Result<Void> deleteCategory(@ApiParam("分类ID") @PathVariable Long id) {
        log.info("管理后台删除分类：{}", id);
        
        adminCategoryService.deleteCategory(id);
        return Result.success("分类删除成功");
    }

    /**
     * 批量删除分类
     *
     * @param ids 分类ID列表
     * @return 删除结果
     */
    @DeleteMapping("/batch")
    @ApiOperation("批量删除分类")
    public Result<Void> batchDeleteCategories(@RequestBody List<Long> ids) {
        log.info("管理后台批量删除分类：{}", ids);
        
        adminCategoryService.batchDeleteCategories(ids);
        return Result.success("分类批量删除成功");
    }

    /**
     * 更新分类排序
     *
     * @param id   分类ID
     * @param sort 排序值
     * @return 更新结果
     */
    @PostMapping("/{id}/sort")
    @ApiOperation("更新分类排序")
    public Result<Void> updateCategorySort(@ApiParam("分类ID") @PathVariable Long id,
                                           @ApiParam("排序值") @RequestParam Integer sort) {
        log.info("管理后台更新分类排序：{} -> {}", id, sort);
        
        adminCategoryService.updateCategorySort(id, sort);
        return Result.success("分类排序更新成功");
    }

    /**
     * 检查分类名称是否存在
     *
     * @param name 分类名称
     * @param id   分类ID（更新时排除自己）
     * @return 检查结果
     */
    @GetMapping("/check-name")
    @ApiOperation("检查分类名称是否存在")
    public Result<Boolean> checkCategoryName(@RequestParam String name,
                                             @RequestParam(required = false) Long id) {
        log.debug("管理后台检查分类名称：{}", name);
        
        boolean exists = adminCategoryService.checkCategoryNameExists(name, id);
        return Result.success(!exists);
    }
}
