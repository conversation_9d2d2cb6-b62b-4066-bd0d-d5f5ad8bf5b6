@echo off
echo ========================================
echo 🛠️ CORS修复验证脚本
echo ========================================
echo.

echo 📋 验证步骤：
echo 1. 检查后端服务状态
echo 2. 测试CORS配置
echo 3. 打开测试页面
echo 4. 提供下一步指导
echo.

echo [1/4] 检查后端服务状态...
curl -s -o nul -w "HTTP状态码: %%{http_code}" http://localhost:8080/api/admin/test/health
if %errorlevel% neq 0 (
    echo ❌ 后端服务未启动或无法访问
    echo.
    echo 🔧 请先启动后端服务：
    echo    cd yangans-admin
    echo    mvn spring-boot:run
    echo.
    echo 启动后重新运行此脚本。
    pause
    exit /b 1
) else (
    echo ✅ 后端服务正常运行
)
echo.

echo [2/4] 测试CORS配置...
echo 测试OPTIONS预检请求...
curl -s -H "Origin: http://localhost:3002" ^
     -H "Access-Control-Request-Method: GET" ^
     -H "Access-Control-Request-Headers: Authorization" ^
     -X OPTIONS ^
     -w "状态码: %%{http_code}\n" ^
     http://localhost:8080/api/admin/test/cors > nul

if %errorlevel% equ 0 (
    echo ✅ CORS预检请求成功
) else (
    echo ❌ CORS预检请求失败
)
echo.

echo [3/4] 打开CORS测试页面...
echo 正在打开独立测试页面...
start quick-cors-test.html
timeout /t 2 /nobreak > nul

echo ✅ 测试页面已打开
echo.

echo [4/4] 下一步指导
echo ========================================
echo 🎯 请按照以下步骤完成验证：
echo.
echo 📱 在打开的测试页面中：
echo   1. 点击"运行所有测试"按钮
echo   2. 观察测试结果（应该全部通过）
echo   3. 检查浏览器控制台（F12）无CORS错误
echo.
echo 🌐 启动前端服务测试：
echo   1. 打开新的命令行窗口
echo   2. cd yangans-web/admin-panel
echo   3. npm run dev
echo   4. 访问 http://localhost:3002
echo   5. 进入文章管理页面测试
echo.
echo 📊 验证成功标志：
echo   ✅ 测试页面所有测试通过
echo   ✅ 浏览器控制台无CORS错误
echo   ✅ 文章管理页面正常加载
echo   ✅ API请求正常工作
echo.
echo 🔧 如果仍有问题：
echo   1. 查看后端控制台日志
echo   2. 检查浏览器Network标签页
echo   3. 参考"CORS修复完整解决方案.md"文档
echo.
echo ========================================
echo 🎉 CORS修复验证完成！
echo ========================================
pause
