import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login/LoginView.vue'),
    meta: {
      title: '管理员登录',
      requiresAuth: false,
      hideInMenu: true
    }
  },
  {
    path: '/',
    name: 'Layout',
    component: () => import('@/layouts/AdminLayout.vue'),
    redirect: '/dashboard',
    meta: {
      requiresAuth: true
    },
    children: [
      {
        path: '/dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard/DashboardView.vue'),
        meta: {
          title: '仪表板',
          icon: 'Dashboard',
          requiresAuth: true
        }
      },
      {
        path: '/articles',
        name: 'ArticleList',
        component: () => import('@/views/Articles/ArticleList.vue'),
        meta: {
          title: '文章管理',
          icon: 'Document',
          requiresAuth: true
        }
      },
      {
        path: '/articles/create',
        name: 'ArticleCreate',
        component: () => import('@/views/Articles/ArticleEdit.vue'),
        meta: {
          title: '新建文章',
          requiresAuth: true,
          hideInMenu: true
        }
      },
      {
        path: '/articles/edit/:id',
        name: 'ArticleEdit',
        component: () => import('@/views/Articles/ArticleEdit.vue'),
        meta: {
          title: '编辑文章',
          requiresAuth: true,
          hideInMenu: true
        }
      },
      {
        path: '/messages',
        name: 'Messages',
        component: () => import('@/views/Messages/MessageList.vue'),
        meta: {
          title: '留言管理',
          icon: 'ChatDotRound',
          requiresAuth: true
        }
      },
      {
        path: '/life-records',
        name: 'LifeRecordList',
        component: () => import('@/views/LifeRecords/LifeRecordList.vue'),
        meta: {
          title: '生活记录',
          icon: 'PictureFilled',
          requiresAuth: true
        }
      },
      {
        path: '/life-records/create',
        name: 'LifeRecordCreate',
        component: () => import('@/views/LifeRecords/LifeRecordEdit.vue'),
        meta: {
          title: '新建记录',
          requiresAuth: true,
          hideInMenu: true
        }
      },
      {
        path: '/life-records/edit/:id',
        name: 'LifeRecordEdit',
        component: () => import('@/views/LifeRecords/LifeRecordEdit.vue'),
        meta: {
          title: '编辑记录',
          requiresAuth: true,
          hideInMenu: true
        }
      },
      {
        path: '/anime',
        name: 'AnimeList',
        component: () => import('@/views/Anime/AnimeList.vue'),
        meta: {
          title: '番剧管理',
          icon: 'VideoPlay',
          requiresAuth: true
        }
      },
      {
        path: '/anime/create',
        name: 'AnimeCreate',
        component: () => import('@/views/Anime/AnimeEdit.vue'),
        meta: {
          title: '添加番剧',
          requiresAuth: true,
          hideInMenu: true
        }
      },
      {
        path: '/anime/edit/:id',
        name: 'AnimeEdit',
        component: () => import('@/views/Anime/AnimeEdit.vue'),
        meta: {
          title: '编辑番剧',
          requiresAuth: true,
          hideInMenu: true
        }
      },
      {
        path: '/settings/profile',
        name: 'ProfileSettings',
        component: () => import('@/views/Settings/ProfileSettings.vue'),
        meta: {
          title: '个人资料',
          icon: 'User',
          requiresAuth: true
        }
      },
      {
        path: '/settings/site',
        name: 'SiteSettings',
        component: () => import('@/views/Settings/SiteSettings.vue'),
        meta: {
          title: '网站设置',
          icon: 'Setting',
          requiresAuth: true
        }
      },
      {
        path: '/test/routes',
        name: 'RouteTest',
        component: () => import('@/views/Test/RouteTest.vue'),
        meta: {
          title: '路由测试',
          icon: 'Tools',
          requiresAuth: true,
          hideInMenu: false
        }
      },
      {
        path: '/test/auth',
        name: 'AuthTest',
        component: () => import('@/views/Test/AuthTest.vue'),
        meta: {
          title: '认证测试',
          icon: 'User',
          requiresAuth: true,
          hideInMenu: false
        }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/Error/NotFoundView.vue'),
    meta: {
      title: '页面未找到',
      hideInMenu: true
    }
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    }
    if (to.hash) {
      return {
        el: to.hash,
        behavior: 'smooth'
      }
    }
    return { top: 0, behavior: 'smooth' }
  }
})

// 全局前置守卫
router.beforeEach(async (to, from, next) => {
  try {
    // 设置页面标题
    if (to.meta.title) {
      document.title = `${to.meta.title} - YangAns管理后台`
    }

    // 🔒 正常认证模式：完整的认证检查逻辑
    const authStore = useAuthStore()

    // 初始化认证状态
    if (!authStore.user && authStore.token) {
      await authStore.initAuth()
    }

    // 检查是否需要认证
    if (to.meta.requiresAuth) {
      if (!authStore.isLoggedIn) {
        ElMessage.warning('请先登录')
        next({
          path: '/login',
          query: { redirect: to.fullPath }
        })
        return
      }

      // 检查管理员权限
      if (!authStore.isAdmin) {
        ElMessage.error('权限不足')
        next('/login')
        return
      }
    }

    // 如果已登录且访问登录页，重定向到仪表板
    if (to.path === '/login' && authStore.isLoggedIn) {
      next('/dashboard')
      return
    }

    next()
  } catch (error) {
    console.error('路由守卫错误:', error)
    // 发生错误时，跳转到登录页
    next('/login')
    return
  }
})

// 全局后置钩子
router.afterEach((to, from) => {
  // 页面加载完成后的处理
  // 可以在这里添加页面访问统计等逻辑
})

export default router
