@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 🧪 认证问题修复验证脚本
echo ========================================
echo.

echo [1/4] 检查前端服务状态...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:3003' -UseBasicParsing -TimeoutSec 5; Write-Host '[成功] 前端服务正常运行 (端口3003)' -ForegroundColor Green } catch { Write-Host '[失败] 前端服务未启动' -ForegroundColor Red }"

echo.
echo [2/4] 检查后端服务状态...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8080/api/admin/categories' -Method OPTIONS -UseBasicParsing -TimeoutSec 5; Write-Host '[成功] 后端服务正常运行 (端口8080)' -ForegroundColor Green } catch { Write-Host '[失败] 后端服务未启动' -ForegroundColor Red }"

echo.
echo [3/4] 检查开发模式配置...
if exist "yangans-web\admin-panel\.env" (
    findstr "VITE_SKIP_AUTH=true" yangans-web\admin-panel\.env >nul
    if !errorlevel! equ 0 (
        echo [成功] 开发模式已启用 (VITE_SKIP_AUTH=true)
    ) else (
        echo [警告] 开发模式未启用
    )
) else (
    echo [警告] 环境配置文件不存在
)

echo.
echo [4/4] 验证修复效果...
echo.
echo 🔧 修复内容：
echo   ✅ API响应拦截器：开发模式跳过401错误处理
echo   ✅ 未授权处理：开发模式跳过登录过期提示
echo   ✅ 路由跳转：使用Vue Router替代window.location
echo   ✅ 测试页面：添加认证状态测试页面
echo.
echo 📋 测试步骤：
echo   1. 访问管理后台：http://localhost:3003
echo   2. 进入文章管理页面
echo   3. 检查是否还有"登录状态已过期"提示
echo   4. 访问认证测试页面：http://localhost:3003/test/auth
echo.
echo 🎯 预期结果：
echo   - 不再显示"登录状态已过期"提示
echo   - 页面正常加载，无认证错误
echo   - 开发模式下所有功能正常使用
echo.

pause
