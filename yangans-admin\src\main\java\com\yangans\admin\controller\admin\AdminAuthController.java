package com.yangans.admin.controller.admin;

import com.yangans.admin.common.Result;
import com.yangans.admin.domain.dto.LoginDTO;
import com.yangans.admin.domain.vo.LoginVO;
import com.yangans.admin.domain.vo.UserVO;
import com.yangans.admin.service.admin.AdminAuthService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * 管理后台认证控制器
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@RestController
@RequestMapping("/admin/auth")
@RequiredArgsConstructor
@Validated
@Api(tags = "管理后台-认证管理")
public class AdminAuthController {

    private final AdminAuthService adminAuthService;

    /**
     * 管理员登录
     *
     * @param loginDTO 登录信息
     * @param request  请求对象
     * @return 登录结果
     */
    @PostMapping("/login")
    @ApiOperation("管理员登录")
    public Result<LoginVO> login(@Valid @RequestBody LoginDTO loginDTO, HttpServletRequest request) {
        log.info("管理员登录请求：{}", loginDTO.getUsername());
        
        String clientIp = getClientIp(request);
        LoginVO loginVO = adminAuthService.login(loginDTO, clientIp);
        
        log.info("管理员登录成功：{}", loginDTO.getUsername());
        return Result.success("登录成功", loginVO);
    }

    /**
     * 管理员登出
     *
     * @param request 请求对象
     * @return 登出结果
     */
    @PostMapping("/logout")
    @ApiOperation("管理员登出")
    public Result<Void> logout(HttpServletRequest request) {
        String token = getTokenFromRequest(request);
        adminAuthService.logout(token);
        
        log.info("管理员登出成功");
        return Result.success("登出成功");
    }

    /**
     * 获取当前用户信息
     *
     * @param request 请求对象
     * @return 用户信息
     */
    @GetMapping("/info")
    @ApiOperation("获取当前用户信息")
    public Result<UserVO> getCurrentUser(HttpServletRequest request) {
        String token = getTokenFromRequest(request);
        UserVO userVO = adminAuthService.getCurrentUser(token);
        
        return Result.success(userVO);
    }

    /**
     * 刷新Token
     *
     * @param request 请求对象
     * @return 新的Token信息
     */
    @PostMapping("/refresh")
    @ApiOperation("刷新Token")
    public Result<LoginVO> refreshToken(HttpServletRequest request) {
        String token = getTokenFromRequest(request);
        LoginVO loginVO = adminAuthService.refreshToken(token);
        
        return Result.success("Token刷新成功", loginVO);
    }

    /**
     * 修改密码
     *
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @param request     请求对象
     * @return 修改结果
     */
    @PostMapping("/change-password")
    @ApiOperation("修改密码")
    public Result<Void> changePassword(@RequestParam String oldPassword,
                                       @RequestParam String newPassword,
                                       HttpServletRequest request) {
        String token = getTokenFromRequest(request);
        adminAuthService.changePassword(token, oldPassword, newPassword);
        
        log.info("管理员修改密码成功");
        return Result.success("密码修改成功");
    }

    /**
     * 从请求中获取Token
     *
     * @param request 请求对象
     * @return Token
     */
    private String getTokenFromRequest(HttpServletRequest request) {
        String authorization = request.getHeader("Authorization");
        if (authorization != null && authorization.startsWith("Bearer ")) {
            return authorization.substring(7);
        }
        return null;
    }

    /**
     * 获取客户端IP地址
     *
     * @param request 请求对象
     * @return IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
