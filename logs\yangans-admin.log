2025-07-31 00:34:55.383 [main] INFO  com.yangans.admin.YangAnsApplication - Starting YangAnsApplication using Java 1.8.0_131 on LAPTOP-O0M94MPC with PID 44072 (D:\yangans-website\yangans-admin\target\classes started by YangAns in D:\yangans-website)
2025-07-31 00:34:55.389 [main] DEBUG com.yangans.admin.YangAnsApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-31 00:34:55.389 [main] INFO  com.yangans.admin.YangAnsApplication - The following 1 profile is active: "dev"
2025-07-31 00:34:56.217 [main] DEBUG c.b.m.autoconfigure.MybatisPlusAutoConfiguration - Searching for mappers annotated with @Mapper
2025-07-31 00:34:56.218 [main] DEBUG c.b.m.autoconfigure.MybatisPlusAutoConfiguration - Using auto-configuration base package 'com.yangans.admin'
2025-07-31 00:34:56.279 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-31 00:34:56.281 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-31 00:34:56.312 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 Redis repository interfaces.
2025-07-31 00:34:56.458 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\yangans-website\yangans-admin\target\classes\com\yangans\admin\mapper\ArticleMapper.class]
2025-07-31 00:34:56.459 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\yangans-website\yangans-admin\target\classes\com\yangans\admin\mapper\ArticleTagMapper.class]
2025-07-31 00:34:56.459 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\yangans-website\yangans-admin\target\classes\com\yangans\admin\mapper\CategoryMapper.class]
2025-07-31 00:34:56.459 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\yangans-website\yangans-admin\target\classes\com\yangans\admin\mapper\TagMapper.class]
2025-07-31 00:34:56.459 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\yangans-website\yangans-admin\target\classes\com\yangans\admin\mapper\UserMapper.class]
2025-07-31 00:34:56.459 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'articleMapper' and 'com.yangans.admin.mapper.ArticleMapper' mapperInterface
2025-07-31 00:34:56.463 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'articleTagMapper' and 'com.yangans.admin.mapper.ArticleTagMapper' mapperInterface
2025-07-31 00:34:56.464 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'categoryMapper' and 'com.yangans.admin.mapper.CategoryMapper' mapperInterface
2025-07-31 00:34:56.465 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'tagMapper' and 'com.yangans.admin.mapper.TagMapper' mapperInterface
2025-07-31 00:34:56.466 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.yangans.admin.mapper.UserMapper' mapperInterface
2025-07-31 00:34:57.079 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-31 00:34:57.088 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-31 00:34:57.090 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-31 00:34:57.264 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-31 00:34:57.264 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1831 ms
2025-07-31 00:34:58.414 [main] DEBUG c.y.a.component.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-07-31 00:34:59.252 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 63 mappings in 'requestMappingHandlerMapping'
2025-07-31 00:34:59.513 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-07-31 00:34:59.529 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/public/**']
2025-07-31 00:34:59.529 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/admin/auth/login']
2025-07-31 00:34:59.529 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-07-31 00:34:59.530 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-07-31 00:34:59.530 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/swagger-resources/**']
2025-07-31 00:34:59.530 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-07-31 00:34:59.530 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/actuator/**']
2025-07-31 00:34:59.530 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-07-31 00:34:59.530 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [authenticated] for Ant [pattern='/admin/**']
2025-07-31 00:34:59.530 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [authenticated] for any request
2025-07-31 00:34:59.540 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@7bb25046, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@69b1e8f8, org.springframework.security.web.context.SecurityContextPersistenceFilter@1868ed54, org.springframework.security.web.header.HeaderWriterFilter@65a4b9d6, org.springframework.web.filter.CorsFilter@126af200, org.springframework.security.web.authentication.logout.LogoutFilter@299786b1, com.yangans.admin.component.security.JwtAuthenticationFilter@6658f08a, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@45790cb, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@73a5d86c, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1e000a17, org.springframework.security.web.session.SessionManagementFilter@5f9a8ddc, org.springframework.security.web.access.ExceptionTranslationFilter@3dea1ecc, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@21e702b8]
2025-07-31 00:34:59.732 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-07-31 00:34:59.787 [main] DEBUG o.s.web.servlet.handler.SimpleUrlHandlerMapping - Patterns [/swagger-ui/] in 'viewControllerHandlerMapping'
2025-07-31 00:34:59.812 [main] DEBUG o.s.web.servlet.handler.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /swagger-ui/**] in 'resourceHandlerMapping'
2025-07-31 00:34:59.822 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice
2025-07-31 00:34:59.844 [main] DEBUG c.b.m.autoconfigure.MybatisPlusAutoConfiguration - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
2025-07-31 00:35:00.090 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8080 is already in use
2025-07-31 00:35:00.115 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-31 00:35:00.125 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-31 00:35:00.155 [main] ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8080 was already in use.

Action:

Identify and stop the process that's listening on port 8080 or configure this application to listen on another port.

2025-07-31 00:35:17.012 [main] INFO  com.yangans.admin.YangAnsApplication - Starting YangAnsApplication using Java 1.8.0_131 on LAPTOP-O0M94MPC with PID 25708 (D:\yangans-website\yangans-admin\target\classes started by YangAns in D:\yangans-website)
2025-07-31 00:35:17.013 [main] DEBUG com.yangans.admin.YangAnsApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-31 00:35:17.014 [main] INFO  com.yangans.admin.YangAnsApplication - The following 1 profile is active: "dev"
2025-07-31 00:35:17.826 [main] DEBUG c.b.m.autoconfigure.MybatisPlusAutoConfiguration - Searching for mappers annotated with @Mapper
2025-07-31 00:35:17.827 [main] DEBUG c.b.m.autoconfigure.MybatisPlusAutoConfiguration - Using auto-configuration base package 'com.yangans.admin'
2025-07-31 00:35:17.889 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-31 00:35:17.893 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-31 00:35:17.924 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 Redis repository interfaces.
2025-07-31 00:35:18.070 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\yangans-website\yangans-admin\target\classes\com\yangans\admin\mapper\ArticleMapper.class]
2025-07-31 00:35:18.070 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\yangans-website\yangans-admin\target\classes\com\yangans\admin\mapper\ArticleTagMapper.class]
2025-07-31 00:35:18.070 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\yangans-website\yangans-admin\target\classes\com\yangans\admin\mapper\CategoryMapper.class]
2025-07-31 00:35:18.070 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\yangans-website\yangans-admin\target\classes\com\yangans\admin\mapper\TagMapper.class]
2025-07-31 00:35:18.070 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\yangans-website\yangans-admin\target\classes\com\yangans\admin\mapper\UserMapper.class]
2025-07-31 00:35:18.071 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'articleMapper' and 'com.yangans.admin.mapper.ArticleMapper' mapperInterface
2025-07-31 00:35:18.074 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'articleTagMapper' and 'com.yangans.admin.mapper.ArticleTagMapper' mapperInterface
2025-07-31 00:35:18.075 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'categoryMapper' and 'com.yangans.admin.mapper.CategoryMapper' mapperInterface
2025-07-31 00:35:18.076 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'tagMapper' and 'com.yangans.admin.mapper.TagMapper' mapperInterface
2025-07-31 00:35:18.077 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.yangans.admin.mapper.UserMapper' mapperInterface
2025-07-31 00:35:18.674 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-31 00:35:18.685 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-31 00:35:18.685 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-31 00:35:18.848 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-31 00:35:18.848 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1786 ms
2025-07-31 00:35:19.929 [main] DEBUG c.y.a.component.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-07-31 00:35:20.754 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 63 mappings in 'requestMappingHandlerMapping'
2025-07-31 00:35:21.029 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-07-31 00:35:21.046 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/public/**']
2025-07-31 00:35:21.046 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/admin/auth/login']
2025-07-31 00:35:21.046 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-07-31 00:35:21.046 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-07-31 00:35:21.046 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/swagger-resources/**']
2025-07-31 00:35:21.046 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-07-31 00:35:21.046 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/actuator/**']
2025-07-31 00:35:21.046 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-07-31 00:35:21.046 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [authenticated] for Ant [pattern='/admin/**']
2025-07-31 00:35:21.046 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [authenticated] for any request
2025-07-31 00:35:21.056 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@2904bc56, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7bb25046, org.springframework.security.web.context.SecurityContextPersistenceFilter@256a5df0, org.springframework.security.web.header.HeaderWriterFilter@8d5da7e, org.springframework.web.filter.CorsFilter@69b1e8f8, org.springframework.security.web.authentication.logout.LogoutFilter@687e561b, com.yangans.admin.component.security.JwtAuthenticationFilter@6598caab, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@131777e8, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@45790cb, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@126af200, org.springframework.security.web.session.SessionManagementFilter@21e39b82, org.springframework.security.web.access.ExceptionTranslationFilter@990b86b, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@29bd056a]
2025-07-31 00:35:21.257 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-07-31 00:35:21.317 [main] DEBUG o.s.web.servlet.handler.SimpleUrlHandlerMapping - Patterns [/swagger-ui/] in 'viewControllerHandlerMapping'
2025-07-31 00:35:21.344 [main] DEBUG o.s.web.servlet.handler.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /swagger-ui/**] in 'resourceHandlerMapping'
2025-07-31 00:35:21.354 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice
2025-07-31 00:35:21.374 [main] DEBUG c.b.m.autoconfigure.MybatisPlusAutoConfiguration - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
2025-07-31 00:35:21.644 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/api'
2025-07-31 00:35:22.174 [main] INFO  com.yangans.admin.YangAnsApplication - Started YangAnsApplication in 5.625 seconds (JVM running for 6.607)
2025-07-31 00:35:58.886 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-31 00:35:58.886 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-31 00:35:58.887 [http-nio-8080-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - Detected StandardServletMultipartResolver
2025-07-31 00:35:58.887 [http-nio-8080-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - Detected AcceptHeaderLocaleResolver
2025-07-31 00:35:58.887 [http-nio-8080-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - Detected FixedThemeResolver
2025-07-31 00:35:58.890 [http-nio-8080-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@50607889
2025-07-31 00:35:58.890 [http-nio-8080-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.support.SessionFlashMapManager@31dc99c5
2025-07-31 00:35:58.890 [http-nio-8080-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-07-31 00:35:58.891 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-07-31 00:35:58.939 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /admin/auth/login
2025-07-31 00:35:58.952 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-31 00:35:58.975 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-31 00:35:59.002 [http-nio-8080-exec-1] DEBUG o.s.s.w.access.intercept.FilterSecurityInterceptor - Authorized filter invocation [POST /admin/auth/login] with attributes [permitAll]
2025-07-31 00:35:59.003 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /admin/auth/login
2025-07-31 00:35:59.006 [http-nio-8080-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - POST "/api/admin/auth/login", parameters={}
2025-07-31 00:35:59.013 [http-nio-8080-exec-1] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.yangans.admin.controller.admin.AdminAuthController#login(LoginDTO, HttpServletRequest)
2025-07-31 00:35:59.274 [http-nio-8080-exec-1] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [LoginDTO(username=admin, password=admin123, rememberMe=false)]
2025-07-31 00:35:59.620 [http-nio-8080-exec-1] INFO  c.y.admin.controller.admin.AdminAuthController - 管理员登录请求：admin
2025-07-31 00:35:59.851 [http-nio-8080-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - YangansHikariCP - Starting...
2025-07-31 00:36:00.456 [http-nio-8080-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - YangansHikariCP - Start completed.
2025-07-31 00:36:00.808 [http-nio-8080-exec-1] INFO  c.y.admin.service.admin.impl.AdminAuthServiceImpl - 使用临时管理员密码登录成功
2025-07-31 00:36:01.633 [http-nio-8080-exec-1] INFO  c.y.admin.controller.admin.AdminAuthController - 管理员登录成功：admin
2025-07-31 00:36:01.682 [http-nio-8080-exec-1] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-31 00:36:01.684 [http-nio-8080-exec-1] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [Result{code=200, message='登录成功', data=LoginVO(accessToken=eyJhbGciOiJIUzUxMiJ9.eyJ1c2VySWQiOjEsInVzZ (truncated)...]
2025-07-31 00:36:01.727 [http-nio-8080-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - Completed 200 OK
2025-07-31 00:36:01.730 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-31 00:46:55.556 [main] INFO  com.yangans.admin.YangAnsApplication - Starting YangAnsApplication using Java 1.8.0_131 on LAPTOP-O0M94MPC with PID 46912 (D:\yangans-website\yangans-admin\target\classes started by YangAns in D:\yangans-website)
2025-07-31 00:46:55.558 [main] DEBUG com.yangans.admin.YangAnsApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-31 00:46:55.558 [main] INFO  com.yangans.admin.YangAnsApplication - The following 1 profile is active: "dev"
2025-07-31 00:46:56.395 [main] DEBUG c.b.m.autoconfigure.MybatisPlusAutoConfiguration - Searching for mappers annotated with @Mapper
2025-07-31 00:46:56.396 [main] DEBUG c.b.m.autoconfigure.MybatisPlusAutoConfiguration - Using auto-configuration base package 'com.yangans.admin'
2025-07-31 00:46:56.459 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-31 00:46:56.463 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-31 00:46:56.493 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Redis repository interfaces.
2025-07-31 00:46:56.644 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\yangans-website\yangans-admin\target\classes\com\yangans\admin\mapper\ArticleMapper.class]
2025-07-31 00:46:56.644 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\yangans-website\yangans-admin\target\classes\com\yangans\admin\mapper\ArticleTagMapper.class]
2025-07-31 00:46:56.644 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\yangans-website\yangans-admin\target\classes\com\yangans\admin\mapper\CategoryMapper.class]
2025-07-31 00:46:56.644 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\yangans-website\yangans-admin\target\classes\com\yangans\admin\mapper\TagMapper.class]
2025-07-31 00:46:56.645 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\yangans-website\yangans-admin\target\classes\com\yangans\admin\mapper\UserMapper.class]
2025-07-31 00:46:56.645 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'articleMapper' and 'com.yangans.admin.mapper.ArticleMapper' mapperInterface
2025-07-31 00:46:56.649 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'articleTagMapper' and 'com.yangans.admin.mapper.ArticleTagMapper' mapperInterface
2025-07-31 00:46:56.650 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'categoryMapper' and 'com.yangans.admin.mapper.CategoryMapper' mapperInterface
2025-07-31 00:46:56.652 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'tagMapper' and 'com.yangans.admin.mapper.TagMapper' mapperInterface
2025-07-31 00:46:56.653 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.yangans.admin.mapper.UserMapper' mapperInterface
2025-07-31 00:46:57.284 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-31 00:46:57.299 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-31 00:46:57.299 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-31 00:46:57.471 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-31 00:46:57.471 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1868 ms
2025-07-31 00:46:58.622 [main] DEBUG c.y.a.component.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-07-31 00:46:59.490 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 63 mappings in 'requestMappingHandlerMapping'
2025-07-31 00:46:59.768 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-07-31 00:46:59.785 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/public/**']
2025-07-31 00:46:59.786 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/admin/auth/login']
2025-07-31 00:46:59.786 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-07-31 00:46:59.786 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-07-31 00:46:59.786 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/swagger-resources/**']
2025-07-31 00:46:59.786 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-07-31 00:46:59.786 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/actuator/**']
2025-07-31 00:46:59.786 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-07-31 00:46:59.787 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [authenticated] for Ant [pattern='/admin/**']
2025-07-31 00:46:59.787 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [authenticated] for any request
2025-07-31 00:46:59.798 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@49f50c8f, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@46e6458d, org.springframework.security.web.context.SecurityContextPersistenceFilter@16ef1160, org.springframework.security.web.header.HeaderWriterFilter@407b41e6, org.springframework.web.filter.CorsFilter@6297cb4b, org.springframework.security.web.authentication.logout.LogoutFilter@13d10057, com.yangans.admin.component.security.JwtAuthenticationFilter@5a06904, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@67593f7b, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2773504f, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@65d6640, org.springframework.security.web.session.SessionManagementFilter@40a7974, org.springframework.security.web.access.ExceptionTranslationFilter@6e9f8160, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@32e65852]
2025-07-31 00:47:00.010 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-07-31 00:47:00.077 [main] DEBUG o.s.web.servlet.handler.SimpleUrlHandlerMapping - Patterns [/swagger-ui/] in 'viewControllerHandlerMapping'
2025-07-31 00:47:00.104 [main] DEBUG o.s.web.servlet.handler.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /swagger-ui/**] in 'resourceHandlerMapping'
2025-07-31 00:47:00.117 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice
2025-07-31 00:47:00.143 [main] DEBUG c.b.m.autoconfigure.MybatisPlusAutoConfiguration - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
2025-07-31 00:47:00.452 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/api'
2025-07-31 00:47:01.047 [main] INFO  com.yangans.admin.YangAnsApplication - Started YangAnsApplication in 5.959 seconds (JVM running for 7.023)
2025-07-31 00:52:13.656 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-31 00:52:13.657 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-31 00:52:13.657 [http-nio-8080-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - Detected StandardServletMultipartResolver
2025-07-31 00:52:13.657 [http-nio-8080-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - Detected AcceptHeaderLocaleResolver
2025-07-31 00:52:13.657 [http-nio-8080-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - Detected FixedThemeResolver
2025-07-31 00:52:13.660 [http-nio-8080-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@7fa59637
2025-07-31 00:52:13.661 [http-nio-8080-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.support.SessionFlashMapManager@740c36e5
2025-07-31 00:52:13.661 [http-nio-8080-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-07-31 00:52:13.661 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-07-31 00:52:13.700 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /admin/auth/login
2025-07-31 00:52:13.711 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-31 00:52:13.729 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-31 00:52:13.751 [http-nio-8080-exec-6] DEBUG o.s.s.w.access.intercept.FilterSecurityInterceptor - Authorized filter invocation [POST /admin/auth/login] with attributes [permitAll]
2025-07-31 00:52:13.752 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /admin/auth/login
2025-07-31 00:52:13.755 [http-nio-8080-exec-6] DEBUG org.springframework.web.servlet.DispatcherServlet - POST "/api/admin/auth/login", parameters={}
2025-07-31 00:52:13.761 [http-nio-8080-exec-6] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.yangans.admin.controller.admin.AdminAuthController#login(LoginDTO, HttpServletRequest)
2025-07-31 00:52:13.940 [http-nio-8080-exec-6] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [LoginDTO(username=admin, password=admin123, rememberMe=false)]
2025-07-31 00:52:14.261 [http-nio-8080-exec-6] INFO  c.y.admin.controller.admin.AdminAuthController - 管理员登录请求：admin
2025-07-31 00:52:14.442 [http-nio-8080-exec-6] INFO  com.zaxxer.hikari.HikariDataSource - YangansHikariCP - Starting...
2025-07-31 00:52:14.910 [http-nio-8080-exec-6] INFO  com.zaxxer.hikari.HikariDataSource - YangansHikariCP - Start completed.
2025-07-31 00:52:15.163 [http-nio-8080-exec-6] INFO  c.y.admin.service.admin.impl.AdminAuthServiceImpl - 使用临时管理员密码登录成功
2025-07-31 00:52:15.746 [http-nio-8080-exec-6] INFO  c.y.admin.controller.admin.AdminAuthController - 管理员登录成功：admin
2025-07-31 00:52:15.790 [http-nio-8080-exec-6] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-31 00:52:15.791 [http-nio-8080-exec-6] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [Result{code=200, message='登录成功', data=LoginVO(accessToken=eyJhbGciOiJIUzUxMiJ9.eyJ1c2VySWQiOjEsInVzZ (truncated)...]
2025-07-31 00:52:15.821 [http-nio-8080-exec-6] DEBUG org.springframework.web.servlet.DispatcherServlet - Completed 200 OK
2025-07-31 00:52:15.824 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
