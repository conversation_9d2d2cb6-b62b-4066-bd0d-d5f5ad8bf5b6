package com.yangans.admin.controller.public_;

import com.yangans.admin.common.Result;
import com.yangans.admin.domain.vo.TagVO;
import com.yangans.admin.service.public_.PublicTagService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 主站标签控制器
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@RestController
@RequestMapping("/public/tags")
@RequiredArgsConstructor
@Api(tags = "主站-标签展示")
public class PublicTagController {

    private final PublicTagService publicTagService;

    /**
     * 获取所有标签列表
     *
     * @return 标签列表
     */
    @GetMapping
    @ApiOperation("获取所有标签列表")
    public Result<List<TagVO>> getAllTags() {
        log.debug("主站获取所有标签列表");
        
        List<TagVO> tags = publicTagService.getAllTags();
        return Result.success(tags);
    }

    /**
     * 根据ID查询标签详情
     *
     * @param id 标签ID
     * @return 标签详情
     */
    @GetMapping("/{id}")
    @ApiOperation("根据ID查询标签详情")
    public Result<TagVO> getTagById(@ApiParam("标签ID") @PathVariable Long id) {
        log.debug("主站查询标签详情：{}", id);
        
        TagVO tagVO = publicTagService.getTagById(id);
        return Result.success(tagVO);
    }

    /**
     * 获取热门标签
     *
     * @param limit 限制数量
     * @return 热门标签列表
     */
    @GetMapping("/hot")
    @ApiOperation("获取热门标签")
    public Result<List<TagVO>> getHotTags(@RequestParam(defaultValue = "20") Integer limit) {
        log.debug("主站获取热门标签：limit={}", limit);
        
        List<TagVO> hotTags = publicTagService.getHotTags(limit);
        return Result.success(hotTags);
    }

    /**
     * 获取标签云数据
     *
     * @return 标签云数据
     */
    @GetMapping("/cloud")
    @ApiOperation("获取标签云数据")
    public Result<List<TagVO>> getTagCloud() {
        log.debug("主站获取标签云数据");
        
        List<TagVO> tagCloud = publicTagService.getTagCloud();
        return Result.success(tagCloud);
    }
}
