@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 🔒 完整认证流程测试脚本
echo ========================================
echo.

echo [1/5] 检查服务状态...
echo.
echo 检查后端服务 (端口8080)...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8080/api/admin/categories' -UseBasicParsing -TimeoutSec 5; Write-Host '[失败] 后端认证已启用 (预期401错误)' -ForegroundColor Green } catch { if ($_.Exception.Response.StatusCode -eq 401) { Write-Host '[成功] 后端认证已启用 (401 Unauthorized)' -ForegroundColor Green } else { Write-Host '[失败] 后端服务异常' -ForegroundColor Red } }"

echo.
echo 检查前端服务 (端口3003)...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:3003' -UseBasicParsing -TimeoutSec 5; Write-Host '[成功] 前端服务正常运行' -ForegroundColor Green } catch { Write-Host '[失败] 前端服务未启动' -ForegroundColor Red }"

echo.
echo [2/5] 测试登录接口...
powershell -Command "try { $body = @{ username='admin'; password='admin123' } | ConvertTo-Json; $response = Invoke-RestMethod -Uri 'http://localhost:8080/api/admin/auth/login' -Method POST -Body $body -ContentType 'application/json' -TimeoutSec 10; if ($response.code -eq 200) { Write-Host '[成功] 登录接口正常，token已生成' -ForegroundColor Green; Write-Host '用户信息:' $response.data.userInfo.username -ForegroundColor Cyan } else { Write-Host '[失败] 登录失败:' $response.message -ForegroundColor Red } } catch { Write-Host '[失败] 登录接口异常:' $_.Exception.Message -ForegroundColor Red }"

echo.
echo [3/5] 检查环境配置...
if exist "yangans-web\admin-panel\.env" (
    findstr "VITE_SKIP_AUTH=false" yangans-web\admin-panel\.env >nul
    if !errorlevel! equ 0 (
        echo [成功] 认证模式已启用 (VITE_SKIP_AUTH=false)
    ) else (
        echo [警告] 认证配置可能不正确
    )
) else (
    echo [警告] 环境配置文件不存在
)

echo.
echo [4/5] 检查数据库用户...
echo 数据库中应该存在以下管理员账号：
echo   用户名: admin
echo   密码: admin123 (BCrypt加密)
echo   角色: admin

echo.
echo [5/5] 修复内容总结...
echo.
echo ✅ 已完成的修复：
echo   🔒 后端认证：恢复管理后台接口认证要求
echo   🔒 前端配置：移除开发模式 (VITE_SKIP_AUTH=false)
echo   🔒 API客户端：恢复正常的401错误处理和token管理
echo   🔒 认证Store：移除模拟用户，使用真实API
echo   🔒 路由守卫：恢复完整的认证检查逻辑
echo   🔒 API路径：修正认证接口路径为 /admin/auth/*
echo.
echo 📋 测试步骤：
echo   1. 访问管理后台：http://localhost:3003
echo   2. 应该自动跳转到登录页面
echo   3. 使用账号登录：admin / admin123
echo   4. 登录成功后进入仪表板
echo   5. 访问文章管理等功能页面
echo.
echo 🎯 预期结果：
echo   - 未登录时自动跳转到登录页
echo   - 使用正确账号可以成功登录
echo   - 登录后可以正常访问所有管理功能
echo   - API调用携带有效的JWT token
echo   - token过期时自动跳转到登录页
echo.

pause
