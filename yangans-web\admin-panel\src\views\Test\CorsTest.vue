<template>
  <div class="cors-test">
    <el-card>
      <template #header>
        <h3>🔧 CORS修复验证测试</h3>
      </template>
      
      <el-space direction="vertical" size="large" style="width: 100%">
        <!-- 测试状态 -->
        <el-alert
          v-if="testStatus.message"
          :title="testStatus.message"
          :type="testStatus.type"
          :closable="false"
        />
        
        <!-- 测试按钮 -->
        <el-card shadow="never">
          <template #header>
            <h4>🧪 CORS测试</h4>
          </template>
          
          <el-space wrap>
            <el-button @click="testCorsEndpoint" :loading="loading.cors" type="primary">
              测试CORS接口
            </el-button>
            <el-button @click="testCategoriesAPI" :loading="loading.categories" type="success">
              测试分类API
            </el-button>
            <el-button @click="testArticlesAPI" :loading="loading.articles" type="warning">
              测试文章API
            </el-button>
            <el-button @click="runAllTests" :loading="loading.all" type="danger">
              运行所有测试
            </el-button>
          </el-space>
        </el-card>
        
        <!-- 测试结果 -->
        <el-card v-if="testResults.length > 0" shadow="never">
          <template #header>
            <h4>📊 测试结果</h4>
          </template>
          
          <el-timeline>
            <el-timeline-item
              v-for="(result, index) in testResults"
              :key="index"
              :type="result.success ? 'success' : 'danger'"
              :timestamp="result.timestamp"
            >
              <h4>{{ result.title }}</h4>
              <p>{{ result.message }}</p>
              <div v-if="result.details" class="test-details">
                <pre>{{ JSON.stringify(result.details, null, 2) }}</pre>
              </div>
            </el-timeline-item>
          </el-timeline>
        </el-card>
        
        <!-- 网络请求详情 -->
        <el-card shadow="never">
          <template #header>
            <h4>🌐 网络请求监控</h4>
          </template>
          
          <p>请打开浏览器开发者工具的Network标签页，观察以下内容：</p>
          <ul>
            <li>✅ OPTIONS预检请求应该返回200状态码</li>
            <li>✅ 响应头应该包含Access-Control-Allow-Origin</li>
            <li>✅ 响应头应该包含Access-Control-Allow-Methods</li>
            <li>✅ 响应头应该包含Access-Control-Allow-Headers</li>
            <li>❌ 不应该有CORS相关的错误信息</li>
          </ul>
        </el-card>
      </el-space>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { request } from '@/api/client'

// 响应式数据
const loading = reactive({
  cors: false,
  categories: false,
  articles: false,
  all: false
})

const testStatus = ref({
  message: '',
  type: 'info' as 'success' | 'warning' | 'info' | 'error'
})

const testResults = ref<Array<{
  title: string
  message: string
  success: boolean
  timestamp: string
  details?: any
}>>([])

// 添加测试结果
const addTestResult = (title: string, message: string, success: boolean, details?: any) => {
  testResults.value.unshift({
    title,
    message,
    success,
    timestamp: new Date().toLocaleTimeString(),
    details
  })
}

// 测试CORS专用接口
const testCorsEndpoint = async () => {
  loading.cors = true
  try {
    const response = await request.get('/admin/test/cors')
    addTestResult(
      'CORS测试接口',
      '✅ CORS测试接口调用成功',
      true,
      response
    )
    testStatus.value = {
      message: 'CORS测试接口调用成功！',
      type: 'success'
    }
    ElMessage.success('CORS测试接口调用成功')
  } catch (error: any) {
    addTestResult(
      'CORS测试接口',
      '❌ CORS测试接口调用失败: ' + error.message,
      false,
      error
    )
    testStatus.value = {
      message: 'CORS测试接口调用失败: ' + error.message,
      type: 'error'
    }
    ElMessage.error('CORS测试接口调用失败')
  } finally {
    loading.cors = false
  }
}

// 测试分类API
const testCategoriesAPI = async () => {
  loading.categories = true
  try {
    const response = await request.get('/admin/categories')
    addTestResult(
      '分类API测试',
      '✅ 分类API调用成功',
      true,
      response
    )
    ElMessage.success('分类API调用成功')
  } catch (error: any) {
    addTestResult(
      '分类API测试',
      '❌ 分类API调用失败: ' + error.message,
      false,
      error
    )
    ElMessage.error('分类API调用失败')
  } finally {
    loading.categories = false
  }
}

// 测试文章API
const testArticlesAPI = async () => {
  loading.articles = true
  try {
    const response = await request.get('/admin/articles', {
      params: { current: 1, size: 5 }
    })
    addTestResult(
      '文章API测试',
      '✅ 文章API调用成功',
      true,
      response
    )
    ElMessage.success('文章API调用成功')
  } catch (error: any) {
    addTestResult(
      '文章API测试',
      '❌ 文章API调用失败: ' + error.message,
      false,
      error
    )
    ElMessage.error('文章API调用失败')
  } finally {
    loading.articles = false
  }
}

// 运行所有测试
const runAllTests = async () => {
  loading.all = true
  testResults.value = []
  
  try {
    testStatus.value = {
      message: '正在运行所有测试...',
      type: 'info'
    }
    
    await testCorsEndpoint()
    await new Promise(resolve => setTimeout(resolve, 500))
    
    await testCategoriesAPI()
    await new Promise(resolve => setTimeout(resolve, 500))
    
    await testArticlesAPI()
    
    const successCount = testResults.value.filter(r => r.success).length
    const totalCount = testResults.value.length
    
    if (successCount === totalCount) {
      testStatus.value = {
        message: `🎉 所有测试通过！(${successCount}/${totalCount})`,
        type: 'success'
      }
    } else {
      testStatus.value = {
        message: `⚠️ 部分测试失败 (${successCount}/${totalCount})`,
        type: 'warning'
      }
    }
  } catch (error) {
    testStatus.value = {
      message: '测试过程中发生错误',
      type: 'error'
    }
  } finally {
    loading.all = false
  }
}
</script>

<style lang="scss" scoped>
.cors-test {
  padding: 20px;
  
  .test-details {
    margin-top: 10px;
    
    pre {
      background: #f5f7fa;
      padding: 10px;
      border-radius: 4px;
      overflow-x: auto;
      max-height: 200px;
      font-size: 12px;
      line-height: 1.4;
    }
  }
}
</style>
