// 测试前端登录修复
const http = require('http');

async function testLogin() {
    return new Promise((resolve, reject) => {
        const postData = JSON.stringify({
            username: 'admin',
            password: 'admin123'
        });

        const options = {
            hostname: 'localhost',
            port: 8080,
            path: '/api/admin/auth/login',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Origin': 'http://localhost:3004',
                'Content-Length': Buffer.byteLength(postData)
            }
        };

        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => data += chunk);
            res.on('end', () => {
                try {
                    const response = JSON.parse(data);
                    console.log('📡 后端响应:', JSON.stringify(response, null, 2));
                    
                    if (response.code === 200) {
                        console.log('✅ 登录成功!');
                        console.log('🔑 Token:', response.data.accessToken.substring(0, 50) + '...');
                        console.log('👤 用户信息:', response.data.userInfo);
                        
                        // 验证数据格式是否符合前端期望
                        const { accessToken, userInfo } = response.data;
                        if (accessToken && userInfo && userInfo.id && userInfo.username) {
                            console.log('✅ 数据格式正确，前端应该能正常处理');
                        } else {
                            console.log('❌ 数据格式可能有问题');
                        }
                        
                        resolve(response);
                    } else {
                        console.log('❌ 登录失败:', response.message);
                        reject(new Error(response.message));
                    }
                } catch (e) {
                    console.log('❌ 响应解析失败:', data);
                    reject(e);
                }
            });
        });

        req.on('error', (e) => {
            console.error('❌ 请求失败:', e.message);
            reject(e);
        });

        req.write(postData);
        req.end();
    });
}

async function runTest() {
    console.log('🧪 测试前端登录修复...\n');
    
    try {
        await testLogin();
        console.log('\n🎉 测试完成！现在前端应该可以正常登录了。');
        console.log('\n📝 请在前端登录页面使用以下凭据测试：');
        console.log('   用户名: admin');
        console.log('   密码: admin123');
    } catch (error) {
        console.log('\n❌ 测试失败:', error.message);
    }
}

runTest();
