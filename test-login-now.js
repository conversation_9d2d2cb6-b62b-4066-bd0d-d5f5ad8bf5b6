// 测试登录
const https = require('http');

const postData = JSON.stringify({
    username: 'admin',
    password: 'admin123'
});

const options = {
    hostname: 'localhost',
    port: 8080,
    path: '/api/admin/auth/login',
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
    }
};

const req = https.request(options, (res) => {
    let data = '';
    
    res.on('data', (chunk) => {
        data += chunk;
    });
    
    res.on('end', () => {
        try {
            const response = JSON.parse(data);
            if (response.code === 200) {
                console.log('✅ 登录成功!');
                console.log('用户:', response.data.userInfo.username);
                console.log('角色:', response.data.userInfo.role);
                console.log('Token长度:', response.data.accessToken.length);
            } else {
                console.log('❌ 登录失败:', response.message);
            }
        } catch (e) {
            console.log('❌ 响应解析失败:', data);
        }
    });
});

req.on('error', (e) => {
    console.error('❌ 请求失败:', e.message);
});

req.write(postData);
req.end();
