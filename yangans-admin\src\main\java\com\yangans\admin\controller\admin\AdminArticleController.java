package com.yangans.admin.controller.admin;

import com.yangans.admin.common.PageResult;
import com.yangans.admin.common.Result;
import com.yangans.admin.domain.dto.ArticleDTO;
import com.yangans.admin.domain.dto.ArticleQueryDTO;
import com.yangans.admin.domain.vo.ArticleVO;
import com.yangans.admin.service.admin.AdminArticleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 管理后台文章控制器
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@RestController
@RequestMapping("/admin/articles")
@RequiredArgsConstructor
@Validated
@Api(tags = "管理后台-文章管理")
public class AdminArticleController {

    private final AdminArticleService adminArticleService;

    /**
     * 分页查询文章列表
     *
     * @param query 查询条件
     * @return 文章分页列表
     */
    @GetMapping
    @ApiOperation("分页查询文章列表")
    public Result<PageResult<ArticleVO>> getArticles(@Valid ArticleQueryDTO query) {
        log.debug("管理后台查询文章列表：{}", query);
        
        PageResult<ArticleVO> pageResult = adminArticleService.getArticles(query);
        return Result.success(pageResult);
    }

    /**
     * 根据ID查询文章详情
     *
     * @param id 文章ID
     * @return 文章详情
     */
    @GetMapping("/{id}")
    @ApiOperation("根据ID查询文章详情")
    public Result<ArticleVO> getArticleById(@ApiParam("文章ID") @PathVariable Long id) {
        log.debug("管理后台查询文章详情：{}", id);
        
        ArticleVO articleVO = adminArticleService.getArticleById(id);
        return Result.success(articleVO);
    }

    /**
     * 创建文章
     *
     * @param articleDTO 文章信息
     * @return 创建结果
     */
    @PostMapping
    @ApiOperation("创建文章")
    public Result<ArticleVO> createArticle(@Valid @RequestBody ArticleDTO articleDTO) {
        log.info("管理后台创建文章：{}", articleDTO.getTitle());
        
        ArticleVO articleVO = adminArticleService.createArticle(articleDTO);
        return Result.success("文章创建成功", articleVO);
    }

    /**
     * 更新文章
     *
     * @param id         文章ID
     * @param articleDTO 文章信息
     * @return 更新结果
     */
    @PutMapping("/{id}")
    @ApiOperation("更新文章")
    public Result<ArticleVO> updateArticle(@ApiParam("文章ID") @PathVariable Long id,
                                           @Valid @RequestBody ArticleDTO articleDTO) {
        log.info("管理后台更新文章：{}", id);
        
        articleDTO.setId(id);
        ArticleVO articleVO = adminArticleService.updateArticle(articleDTO);
        return Result.success("文章更新成功", articleVO);
    }

    /**
     * 删除文章
     *
     * @param id 文章ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    @ApiOperation("删除文章")
    public Result<Void> deleteArticle(@ApiParam("文章ID") @PathVariable Long id) {
        log.info("管理后台删除文章：{}", id);
        
        adminArticleService.deleteArticle(id);
        return Result.success("文章删除成功");
    }

    /**
     * 批量删除文章
     *
     * @param ids 文章ID列表
     * @return 删除结果
     */
    @DeleteMapping("/batch")
    @ApiOperation("批量删除文章")
    public Result<Void> batchDeleteArticles(@RequestBody List<Long> ids) {
        log.info("管理后台批量删除文章：{}", ids);
        
        adminArticleService.batchDeleteArticles(ids);
        return Result.success("文章批量删除成功");
    }

    /**
     * 发布文章
     *
     * @param id 文章ID
     * @return 发布结果
     */
    @PostMapping("/{id}/publish")
    @ApiOperation("发布文章")
    public Result<Void> publishArticle(@ApiParam("文章ID") @PathVariable Long id) {
        log.info("管理后台发布文章：{}", id);
        
        adminArticleService.publishArticle(id);
        return Result.success("文章发布成功");
    }

    /**
     * 撤回文章
     *
     * @param id 文章ID
     * @return 撤回结果
     */
    @PostMapping("/{id}/unpublish")
    @ApiOperation("撤回文章")
    public Result<Void> unpublishArticle(@ApiParam("文章ID") @PathVariable Long id) {
        log.info("管理后台撤回文章：{}", id);
        
        adminArticleService.unpublishArticle(id);
        return Result.success("文章撤回成功");
    }

    /**
     * 置顶文章
     *
     * @param id 文章ID
     * @return 置顶结果
     */
    @PostMapping("/{id}/top")
    @ApiOperation("置顶文章")
    public Result<Void> topArticle(@ApiParam("文章ID") @PathVariable Long id) {
        log.info("管理后台置顶文章：{}", id);
        
        adminArticleService.topArticle(id);
        return Result.success("文章置顶成功");
    }

    /**
     * 取消置顶
     *
     * @param id 文章ID
     * @return 取消置顶结果
     */
    @PostMapping("/{id}/untop")
    @ApiOperation("取消置顶")
    public Result<Void> untopArticle(@ApiParam("文章ID") @PathVariable Long id) {
        log.info("管理后台取消置顶文章：{}", id);
        
        adminArticleService.untopArticle(id);
        return Result.success("取消置顶成功");
    }

    /**
     * 批量发布文章
     *
     * @param ids 文章ID列表
     * @return 发布结果
     */
    @PostMapping("/batch/publish")
    @ApiOperation("批量发布文章")
    public Result<Void> batchPublishArticles(@RequestBody List<Long> ids) {
        log.info("管理后台批量发布文章：{}", ids);
        
        adminArticleService.batchPublishArticles(ids);
        return Result.success("文章批量发布成功");
    }

    /**
     * 获取文章统计信息
     *
     * @return 统计信息
     */
    @GetMapping("/statistics")
    @ApiOperation("获取文章统计信息")
    public Result<Map<String, Object>> getArticleStatistics() {
        log.debug("管理后台获取文章统计信息");
        
        Map<String, Object> statistics = adminArticleService.getArticleStatistics();
        return Result.success(statistics);
    }
}
