// 完整认证流程测试
const http = require('http');

async function testLogin() {
    return new Promise((resolve, reject) => {
        const postData = JSON.stringify({
            username: 'admin',
            password: 'admin123'
        });

        const options = {
            hostname: 'localhost',
            port: 8080,
            path: '/api/admin/auth/login',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(postData)
            }
        };

        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => data += chunk);
            res.on('end', () => {
                try {
                    const response = JSON.parse(data);
                    if (response.code === 200) {
                        resolve(response.data.accessToken);
                    } else {
                        reject(new Error(response.message));
                    }
                } catch (e) {
                    reject(new Error('响应解析失败'));
                }
            });
        });

        req.on('error', reject);
        req.write(postData);
        req.end();
    });
}

async function testApiWithToken(token) {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: 'localhost',
            port: 8080,
            path: '/api/admin/categories',
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`
            }
        };

        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => data += chunk);
            res.on('end', () => {
                try {
                    const response = JSON.parse(data);
                    resolve(response);
                } catch (e) {
                    reject(new Error('响应解析失败'));
                }
            });
        });

        req.on('error', reject);
        req.end();
    });
}

async function runTests() {
    console.log('🧪 开始完整认证流程测试...\n');

    try {
        // 1. 测试登录
        console.log('1️⃣ 测试登录...');
        const token = await testLogin();
        console.log('✅ 登录成功');
        console.log(`   Token: ${token.substring(0, 30)}...`);
        console.log(`   Token长度: ${token.length}`);

        // 2. 测试带Token的API调用
        console.log('\n2️⃣ 测试带Token的API调用...');
        const apiResponse = await testApiWithToken(token);
        if (apiResponse.code === 200) {
            console.log('✅ API调用成功');
            console.log(`   获取到 ${apiResponse.data.length} 个分类`);
        } else {
            console.log('❌ API调用失败:', apiResponse.message);
        }

        // 3. 测试无Token的API调用
        console.log('\n3️⃣ 测试无Token的API调用...');
        try {
            await testApiWithToken('');
            console.log('❌ 应该返回401错误');
        } catch (error) {
            console.log('✅ 正确返回401错误');
        }

        console.log('\n🎉 所有测试通过！认证系统工作正常！');
        
    } catch (error) {
        console.log('❌ 测试失败:', error.message);
    }
}

runTests();
