# YangAns Admin Backend Configuration
server:
  port: 8080
  servlet:
    context-path: /api
    encoding:
      charset: UTF-8
      enabled: true
      force: true

spring:
  application:
    name: yangans-admin

  profiles:
    active: dev

  main:
    allow-circular-references: true

  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  
  # 数据源配置
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *****************************************************************************************************************************************************
    username: root
    password: 123456
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: YangansHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1
  
  # Redis配置
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0
    timeout: 10000ms
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0
  
  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
  
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 50MB
      enabled: true

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: ASSIGN_ID
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
      update-strategy: NOT_NULL
      insert-strategy: NOT_NULL
      select-strategy: NOT_EMPTY
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.yangans.admin.domain.entity

# 日志配置
logging:
  level:
    com.yangans.admin: DEBUG
    org.springframework.security: DEBUG
    org.springframework.web: INFO
    org.mybatis: DEBUG
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n'
    file: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n'
  file:
    name: logs/yangans-admin.log
    max-size: 10MB
    max-history: 30

# 自定义配置
yangans:
  # JWT配置
  jwt:
    secret: yangans-blog-jwt-secret-key-2024-for-hs512-algorithm-must-be-at-least-512-bits-long
    expiration: 86400000  # 24小时
    header: Authorization
    prefix: Bearer
  
  # 文件上传配置
  upload:
    path: /uploads/
    max-size: 10485760  # 10MB
    allowed-types: jpg,jpeg,png,gif,webp,pdf,doc,docx
  
  # 跨域配置
  cors:
    allowed-origins:
      - http://localhost:3000
      - http://localhost:3001
      - http://localhost:3002
      - http://localhost:3003
      - http://localhost:3004
    allowed-methods: GET,POST,PUT,DELETE,OPTIONS
    allowed-headers: '*'
    allow-credentials: true
    max-age: 3600

  # API配置
  api:
    public:
      rate-limit: 100  # 主站接口限流：100次/分钟
      cache-ttl: 3600  # 缓存1小时
    admin:
      rate-limit: 1000 # 管理接口限流：1000次/分钟
      cache-ttl: 300   # 缓存5分钟

# Swagger配置
springfox:
  documentation:
    swagger-ui:
      enabled: true
    enabled: true

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized
