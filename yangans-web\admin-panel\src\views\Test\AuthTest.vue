<template>
  <div class="auth-test-container">
    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <span>🧪 认证状态测试</span>
        </div>
      </template>
      
      <div class="test-content">
        <el-descriptions title="当前配置" :column="1" border>
          <el-descriptions-item label="开发模式">
            <el-tag :type="isDevelopment ? 'success' : 'danger'">
              {{ isDevelopment ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="跳过认证">
            <el-tag :type="skipAuth ? 'success' : 'danger'">
              {{ skipAuth ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="API基础URL">
            {{ apiBaseUrl }}
          </el-descriptions-item>
          <el-descriptions-item label="当前路由">
            {{ currentRoute }}
          </el-descriptions-item>
        </el-descriptions>
        
        <el-divider />
        
        <el-descriptions title="认证状态" :column="1" border>
          <el-descriptions-item label="登录状态">
            <el-tag :type="authStore.isLoggedIn ? 'success' : 'danger'">
              {{ authStore.isLoggedIn ? '已登录' : '未登录' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="管理员权限">
            <el-tag :type="authStore.isAdmin ? 'success' : 'warning'">
              {{ authStore.isAdmin ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="用户信息">
            {{ authStore.user ? authStore.user.username : '无' }}
          </el-descriptions-item>
          <el-descriptions-item label="Token">
            {{ authStore.token ? '已设置' : '未设置' }}
          </el-descriptions-item>
        </el-descriptions>
        
        <el-divider />
        
        <div class="test-actions">
          <el-space wrap>
            <el-button type="primary" @click="testApiCall">
              测试API调用
            </el-button>
            <el-button type="success" @click="testLogin">
              测试登录
            </el-button>
            <el-button type="warning" @click="testLogout">
              测试登出
            </el-button>
            <el-button type="info" @click="refreshPage">
              刷新页面
            </el-button>
          </el-space>
        </div>
        
        <el-divider />
        
        <div class="test-results">
          <h4>测试结果：</h4>
          <el-scrollbar height="200px">
            <div class="log-container">
              <div v-for="(log, index) in testLogs" :key="index" class="log-item">
                <span class="log-time">{{ log.time }}</span>
                <span :class="['log-level', `log-${log.level}`]">{{ log.level.toUpperCase() }}</span>
                <span class="log-message">{{ log.message }}</span>
              </div>
            </div>
          </el-scrollbar>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { categoryApi } from '@/api/category'
import { ElMessage } from 'element-plus'

// 状态
const route = useRoute()
const authStore = useAuthStore()
const testLogs = ref<Array<{ time: string; level: string; message: string }>>([])

// 计算属性
const isDevelopment = computed(() => import.meta.env.DEV || import.meta.env.VITE_APP_ENV === 'development')
const skipAuth = computed(() => import.meta.env.VITE_SKIP_AUTH === 'true')
const apiBaseUrl = computed(() => import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080/api')
const currentRoute = computed(() => route.fullPath)

// 添加日志
const addLog = (level: string, message: string) => {
  const now = new Date()
  testLogs.value.unshift({
    time: now.toLocaleTimeString(),
    level,
    message
  })
  
  // 限制日志数量
  if (testLogs.value.length > 50) {
    testLogs.value = testLogs.value.slice(0, 50)
  }
}

// 测试API调用
const testApiCall = async () => {
  addLog('info', '开始测试API调用...')
  
  try {
    const response = await categoryApi.getAllCategories()
    addLog('success', `API调用成功: ${JSON.stringify(response)}`)
    ElMessage.success('API调用成功')
  } catch (error: any) {
    addLog('error', `API调用失败: ${error.message}`)
    ElMessage.error('API调用失败')
  }
}

// 测试登录
const testLogin = async () => {
  addLog('info', '开始测试登录...')
  
  try {
    const success = await authStore.login({
      username: 'admin',
      password: 'admin123',
      remember: false
    })
    
    if (success) {
      addLog('success', '登录测试成功')
      ElMessage.success('登录测试成功')
    } else {
      addLog('error', '登录测试失败')
      ElMessage.error('登录测试失败')
    }
  } catch (error: any) {
    addLog('error', `登录测试异常: ${error.message}`)
    ElMessage.error('登录测试异常')
  }
}

// 测试登出
const testLogout = async () => {
  addLog('info', '开始测试登出...')
  
  try {
    await authStore.logout()
    addLog('success', '登出测试成功')
    ElMessage.success('登出测试成功')
  } catch (error: any) {
    addLog('error', `登出测试异常: ${error.message}`)
    ElMessage.error('登出测试异常')
  }
}

// 刷新页面
const refreshPage = () => {
  addLog('info', '刷新页面...')
  window.location.reload()
}

// 初始化
onMounted(() => {
  addLog('info', '认证测试页面已加载')
  addLog('info', `开发模式: ${isDevelopment.value}`)
  addLog('info', `跳过认证: ${skipAuth.value}`)
  addLog('info', `API基础URL: ${apiBaseUrl.value}`)
})
</script>

<style scoped>
.auth-test-container {
  padding: 20px;
}

.test-card {
  max-width: 800px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

.test-content {
  padding: 20px 0;
}

.test-actions {
  text-align: center;
  margin: 20px 0;
}

.test-results {
  margin-top: 20px;
}

.log-container {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
}

.log-item {
  display: flex;
  margin-bottom: 4px;
  line-height: 1.4;
}

.log-time {
  color: #666;
  margin-right: 8px;
  min-width: 80px;
}

.log-level {
  margin-right: 8px;
  min-width: 60px;
  font-weight: bold;
}

.log-info {
  color: #409eff;
}

.log-success {
  color: #67c23a;
}

.log-error {
  color: #f56c6c;
}

.log-warning {
  color: #e6a23c;
}

.log-message {
  flex: 1;
  word-break: break-all;
}
</style>
