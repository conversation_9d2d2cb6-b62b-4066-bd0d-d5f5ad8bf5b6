@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 🔒 最终登录测试
echo ========================================
echo.

echo [1/3] 测试登录接口...
powershell -Command "$body = '{\"username\":\"admin\",\"password\":\"hello\"}'; try { $response = Invoke-RestMethod -Uri 'http://localhost:8080/api/admin/auth/login' -Method POST -Body $body -ContentType 'application/json'; Write-Host '[成功] 登录成功' -ForegroundColor Green; Write-Host 'Token:' $response.data.accessToken.Substring(0, 30) '...' -ForegroundColor Cyan; Write-Host '用户:' $response.data.userInfo.username -ForegroundColor Cyan; Write-Host '角色:' $response.data.userInfo.role -ForegroundColor Cyan } catch { Write-Host '[失败] 登录失败:' $_.Exception.Response.StatusCode -ForegroundColor Red }"

echo.
echo [2/3] 测试带Token的API调用...
powershell -Command "$body = '{\"username\":\"admin\",\"password\":\"hello\"}'; try { $loginResponse = Invoke-RestMethod -Uri 'http://localhost:8080/api/admin/auth/login' -Method POST -Body $body -ContentType 'application/json'; $token = $loginResponse.data.accessToken; $headers = @{ 'Authorization' = 'Bearer ' + $token }; $apiResponse = Invoke-RestMethod -Uri 'http://localhost:8080/api/admin/categories' -Method GET -Headers $headers; Write-Host '[成功] API调用成功，获取到' $apiResponse.data.Count '个分类' -ForegroundColor Green } catch { Write-Host '[失败] API调用失败' -ForegroundColor Red }"

echo.
echo [3/3] 前端测试指南...
echo.
echo 📋 现在可以测试前端登录：
echo   1. 访问：http://localhost:3003
echo   2. 应该自动跳转到登录页面
echo   3. 使用账号：admin / hello
echo   4. 登录成功后进入管理后台
echo.
echo 🎯 预期结果：
echo   - 登录成功，获取到JWT token
echo   - 可以正常访问管理后台功能
echo   - API调用携带正确的认证头
echo.

pause
