package com.yangans.admin.controller.admin;

import com.yangans.admin.common.PageResult;
import com.yangans.admin.common.Result;
import com.yangans.admin.domain.dto.TagDTO;
import com.yangans.admin.domain.vo.TagVO;
import com.yangans.admin.service.admin.AdminTagService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 管理后台标签控制器
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@RestController
@RequestMapping("/admin/tags")
@RequiredArgsConstructor
@Validated
@Api(tags = "管理后台-标签管理")
public class AdminTagController {

    private final AdminTagService adminTagService;

    /**
     * 分页查询标签列表
     *
     * @param current 当前页
     * @param size    每页大小
     * @param name    标签名称（可选）
     * @return 标签分页列表
     */
    @GetMapping
    @ApiOperation("分页查询标签列表")
    public Result<PageResult<TagVO>> getTags(@RequestParam(defaultValue = "1") Long current,
                                             @RequestParam(defaultValue = "10") Long size,
                                             @RequestParam(required = false) String name) {
        log.debug("管理后台查询标签列表：current={}, size={}, name={}", current, size, name);
        
        PageResult<TagVO> pageResult = adminTagService.getTags(current, size, name);
        return Result.success(pageResult);
    }

    /**
     * 获取所有标签列表（不分页）
     *
     * @return 标签列表
     */
    @GetMapping("/all")
    @ApiOperation("获取所有标签列表")
    public Result<List<TagVO>> getAllTags() {
        log.debug("管理后台获取所有标签列表");
        
        List<TagVO> tags = adminTagService.getAllTags();
        return Result.success(tags);
    }

    /**
     * 根据ID查询标签详情
     *
     * @param id 标签ID
     * @return 标签详情
     */
    @GetMapping("/{id}")
    @ApiOperation("根据ID查询标签详情")
    public Result<TagVO> getTagById(@ApiParam("标签ID") @PathVariable Long id) {
        log.debug("管理后台查询标签详情：{}", id);
        
        TagVO tagVO = adminTagService.getTagById(id);
        return Result.success(tagVO);
    }

    /**
     * 创建标签
     *
     * @param tagDTO 标签信息
     * @return 创建结果
     */
    @PostMapping
    @ApiOperation("创建标签")
    public Result<TagVO> createTag(@Valid @RequestBody TagDTO tagDTO) {
        log.info("管理后台创建标签：{}", tagDTO.getName());
        
        TagVO tagVO = adminTagService.createTag(tagDTO);
        return Result.success("标签创建成功", tagVO);
    }

    /**
     * 更新标签
     *
     * @param id     标签ID
     * @param tagDTO 标签信息
     * @return 更新结果
     */
    @PutMapping("/{id}")
    @ApiOperation("更新标签")
    public Result<TagVO> updateTag(@ApiParam("标签ID") @PathVariable Long id,
                                   @Valid @RequestBody TagDTO tagDTO) {
        log.info("管理后台更新标签：{}", id);
        
        tagDTO.setId(id);
        TagVO tagVO = adminTagService.updateTag(tagDTO);
        return Result.success("标签更新成功", tagVO);
    }

    /**
     * 删除标签
     *
     * @param id 标签ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    @ApiOperation("删除标签")
    public Result<Void> deleteTag(@ApiParam("标签ID") @PathVariable Long id) {
        log.info("管理后台删除标签：{}", id);
        
        adminTagService.deleteTag(id);
        return Result.success("标签删除成功");
    }

    /**
     * 批量删除标签
     *
     * @param ids 标签ID列表
     * @return 删除结果
     */
    @DeleteMapping("/batch")
    @ApiOperation("批量删除标签")
    public Result<Void> batchDeleteTags(@RequestBody List<Long> ids) {
        log.info("管理后台批量删除标签：{}", ids);
        
        adminTagService.batchDeleteTags(ids);
        return Result.success("标签批量删除成功");
    }

    /**
     * 获取热门标签
     *
     * @param limit 限制数量
     * @return 热门标签列表
     */
    @GetMapping("/hot")
    @ApiOperation("获取热门标签")
    public Result<List<TagVO>> getHotTags(@RequestParam(defaultValue = "10") Integer limit) {
        log.debug("管理后台获取热门标签：limit={}", limit);
        
        List<TagVO> hotTags = adminTagService.getHotTags(limit);
        return Result.success(hotTags);
    }

    /**
     * 检查标签名称是否存在
     *
     * @param name 标签名称
     * @param id   标签ID（更新时排除自己）
     * @return 检查结果
     */
    @GetMapping("/check-name")
    @ApiOperation("检查标签名称是否存在")
    public Result<Boolean> checkTagName(@RequestParam String name,
                                        @RequestParam(required = false) Long id) {
        log.debug("管理后台检查标签名称：{}", name);
        
        boolean exists = adminTagService.checkTagNameExists(name, id);
        return Result.success(!exists);
    }

    /**
     * 批量创建标签
     *
     * @param names 标签名称列表
     * @return 创建结果
     */
    @PostMapping("/batch")
    @ApiOperation("批量创建标签")
    public Result<List<TagVO>> batchCreateTags(@RequestBody List<String> names) {
        log.info("管理后台批量创建标签：{}", names);
        
        List<TagVO> tags = adminTagService.batchCreateTags(names);
        return Result.success("标签批量创建成功", tags);
    }
}
