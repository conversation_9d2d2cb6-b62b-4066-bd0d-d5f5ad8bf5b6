<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORS修复快速测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background: #2980b9;
        }
        button:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
        }
        .success {
            color: #27ae60;
            font-weight: bold;
        }
        .error {
            color: #e74c3c;
            font-weight: bold;
        }
        .info {
            color: #3498db;
            font-weight: bold;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .result.success {
            background: #d5f4e6;
            border: 1px solid #27ae60;
        }
        .result.error {
            background: #fdf2f2;
            border: 1px solid #e74c3c;
        }
        .result.info {
            background: #e8f4fd;
            border: 1px solid #3498db;
        }
        .instructions {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛠️ CORS修复快速测试</h1>
        
        <div class="instructions">
            <h3>📋 使用说明</h3>
            <ol>
                <li>确保后端服务正在运行 (http://localhost:8080)</li>
                <li>点击下面的测试按钮验证CORS修复效果</li>
                <li>观察测试结果和浏览器控制台</li>
                <li>如果所有测试通过，说明CORS问题已解决</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>🧪 CORS测试</h3>
            <button onclick="testCorsEndpoint()">测试CORS专用接口</button>
            <button onclick="testCategoriesAPI()">测试分类API</button>
            <button onclick="testArticlesAPI()">测试文章API</button>
            <button onclick="runAllTests()">运行所有测试</button>
            <div id="corsResults"></div>
        </div>

        <div class="test-section">
            <h3>🌐 网络监控</h3>
            <p>请打开浏览器开发者工具 (F12) 的 Network 标签页，观察：</p>
            <ul>
                <li>✅ OPTIONS预检请求应该返回 <strong>200</strong> 状态码</li>
                <li>✅ 响应头应该包含 <strong>Access-Control-Allow-Origin</strong></li>
                <li>✅ 响应头应该包含 <strong>Access-Control-Allow-Methods</strong></li>
                <li>❌ 控制台不应该有CORS相关错误</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>📊 测试状态</h3>
            <div id="testStatus">等待测试...</div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080/api';
        let testCount = 0;
        let passedCount = 0;

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('testStatus');
            statusDiv.innerHTML = `<span class="${type}">${message}</span>`;
        }

        function addResult(title, message, success, details = null) {
            const resultsDiv = document.getElementById('corsResults');
            const resultClass = success ? 'success' : 'error';
            const icon = success ? '✅' : '❌';
            
            const resultHtml = `
                <div class="result ${resultClass}">
                    <strong>${icon} ${title}</strong>
                    ${message}
                    ${details ? `\n详情: ${JSON.stringify(details, null, 2)}` : ''}
                </div>
            `;
            
            resultsDiv.innerHTML = resultHtml + resultsDiv.innerHTML;
            
            testCount++;
            if (success) passedCount++;
            
            updateStatus(`测试进度: ${passedCount}/${testCount} 通过`, 
                        passedCount === testCount ? 'success' : 'info');
        }

        async function makeRequest(url, options = {}) {
            const defaultOptions = {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer dev-mock-token'
                },
                ...options
            };

            console.log(`🔧 发送请求: ${url}`, defaultOptions);
            
            try {
                const response = await fetch(url, defaultOptions);
                const data = await response.json();
                
                console.log(`✅ 响应成功: ${response.status}`, data);
                
                if (response.ok) {
                    return { success: true, data, status: response.status };
                } else {
                    return { success: false, error: data, status: response.status };
                }
            } catch (error) {
                console.error(`❌ 请求失败:`, error);
                return { success: false, error: error.message };
            }
        }

        async function testCorsEndpoint() {
            updateStatus('正在测试CORS专用接口...', 'info');
            
            const result = await makeRequest(`${API_BASE}/admin/test/cors`);
            
            if (result.success) {
                addResult(
                    'CORS专用接口测试',
                    `成功调用CORS测试接口，状态码: ${result.status}`,
                    true,
                    result.data
                );
            } else {
                addResult(
                    'CORS专用接口测试',
                    `调用失败: ${result.error}`,
                    false,
                    result
                );
            }
        }

        async function testCategoriesAPI() {
            updateStatus('正在测试分类API...', 'info');
            
            const result = await makeRequest(`${API_BASE}/admin/categories`);
            
            if (result.success) {
                addResult(
                    '分类API测试',
                    `成功调用分类API，状态码: ${result.status}`,
                    true,
                    result.data
                );
            } else {
                addResult(
                    '分类API测试',
                    `调用失败: ${result.error}`,
                    false,
                    result
                );
            }
        }

        async function testArticlesAPI() {
            updateStatus('正在测试文章API...', 'info');
            
            const result = await makeRequest(`${API_BASE}/admin/articles?current=1&size=5`);
            
            if (result.success) {
                addResult(
                    '文章API测试',
                    `成功调用文章API，状态码: ${result.status}`,
                    true,
                    result.data
                );
            } else {
                addResult(
                    '文章API测试',
                    `调用失败: ${result.error}`,
                    false,
                    result
                );
            }
        }

        async function runAllTests() {
            // 重置计数器
            testCount = 0;
            passedCount = 0;
            document.getElementById('corsResults').innerHTML = '';
            
            updateStatus('正在运行所有测试...', 'info');
            
            try {
                await testCorsEndpoint();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                await testCategoriesAPI();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                await testArticlesAPI();
                
                // 最终结果
                if (passedCount === testCount && testCount > 0) {
                    updateStatus(`🎉 所有测试通过！(${passedCount}/${testCount})`, 'success');
                    addResult(
                        '测试总结',
                        '🎉 恭喜！CORS配置修复成功，所有API调用正常工作！',
                        true
                    );
                } else {
                    updateStatus(`⚠️ 部分测试失败 (${passedCount}/${testCount})`, 'error');
                    addResult(
                        '测试总结',
                        '⚠️ 部分测试失败，请检查后端服务状态和CORS配置',
                        false
                    );
                }
            } catch (error) {
                updateStatus('测试过程中发生错误', 'error');
                addResult(
                    '测试错误',
                    `测试过程中发生错误: ${error.message}`,
                    false
                );
            }
        }

        // 页面加载时的提示
        window.onload = function() {
            updateStatus('页面已加载，请点击测试按钮开始验证CORS修复效果', 'info');
            console.log('🔧 CORS测试页面已加载');
            console.log('📋 请确保后端服务正在运行: http://localhost:8080');
        };
    </script>
</body>
</html>
