package com.yangans.admin.controller.public_;

import com.yangans.admin.common.Result;
import com.yangans.admin.domain.vo.CategoryVO;
import com.yangans.admin.service.public_.PublicCategoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 主站分类控制器
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@RestController
@RequestMapping("/public/categories")
@RequiredArgsConstructor
@Api(tags = "主站-分类展示")
public class PublicCategoryController {

    private final PublicCategoryService publicCategoryService;

    /**
     * 获取所有分类列表
     *
     * @return 分类列表
     */
    @GetMapping
    @ApiOperation("获取所有分类列表")
    public Result<List<CategoryVO>> getAllCategories() {
        log.debug("主站获取所有分类列表");
        
        List<CategoryVO> categories = publicCategoryService.getAllCategories();
        return Result.success(categories);
    }

    /**
     * 根据ID查询分类详情
     *
     * @param id 分类ID
     * @return 分类详情
     */
    @GetMapping("/{id}")
    @ApiOperation("根据ID查询分类详情")
    public Result<CategoryVO> getCategoryById(@ApiParam("分类ID") @PathVariable Long id) {
        log.debug("主站查询分类详情：{}", id);
        
        CategoryVO categoryVO = publicCategoryService.getCategoryById(id);
        return Result.success(categoryVO);
    }

    /**
     * 获取热门分类（按文章数量排序）
     *
     * @param limit 限制数量
     * @return 热门分类列表
     */
    @GetMapping("/hot")
    @ApiOperation("获取热门分类")
    public Result<List<CategoryVO>> getHotCategories(@RequestParam(defaultValue = "10") Integer limit) {
        log.debug("主站获取热门分类：limit={}", limit);
        
        List<CategoryVO> hotCategories = publicCategoryService.getHotCategories(limit);
        return Result.success(hotCategories);
    }
}
